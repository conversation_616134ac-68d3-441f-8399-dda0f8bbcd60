"""
CAN Frame Assembler for handling multi-frame messages
Assembles multiple CAN frames into complete protocol messages
"""

import time
from collections import deque


class CANFrameAssembler:
    """Assembles multiple CAN frames into complete messages based on protocol"""
    
    def __init__(self):
        self.pending_frames = []
        self.last_frame_time = 0
        self.frame_timeout = 0.5  # 500ms timeout between frames
        
        # Protocol definitions
        self.protocols = {
            (0xF0, 0x00): {'name': 'module_info', 'frames': 3, 'total_bytes': 21},
            (0xF0, 0x01): {'name': 'bit_status', 'frames': 2, 'total_bytes': 14},
            (0xF0, 0x02): {'name': 'channel_params', 'frames': 'variable'},  # 2 or 3 frames
        }
        
    def add_frame(self, frame_data):
        """Add a CAN frame and check if a complete message is ready
        
        Args:
            frame_data: List of bytes from CAN frame
            
        Returns:
            tuple: (message_type, complete_data) or (None, None) if not complete
        """
        current_time = time.time()
        
        # Check for timeout - clear pending frames if too much time passed
        if self.pending_frames and (current_time - self.last_frame_time) > self.frame_timeout:
            self.pending_frames = []
        
        # If no pending frames, check if this is a known protocol start
        if not self.pending_frames:
            if len(frame_data) >= 2:
                cmd_key = (frame_data[0], frame_data[1])
                if cmd_key in self.protocols:
                    self.pending_frames = [frame_data]
                    self.last_frame_time = current_time
                    
                    # Check if single frame is complete (shouldn't happen with our protocols)
                    return self._check_complete()
            return None, None
        
        # Add frame to pending
        self.pending_frames.append(frame_data)
        self.last_frame_time = current_time
        
        # Check if message is complete
        return self._check_complete()
    
    def _check_complete(self):
        """Check if pending frames form a complete message"""
        if not self.pending_frames:
            return None, None
            
        # Get protocol info based on first frame
        first_frame = self.pending_frames[0]
        if len(first_frame) < 2:
            return None, None
            
        cmd_key = (first_frame[0], first_frame[1])
        if cmd_key not in self.protocols:
            return None, None
            
        protocol = self.protocols[cmd_key]
        
        # Handle variable frame count for channel params
        if protocol['frames'] == 'variable':
            if len(first_frame) >= 3:
                channel_group = first_frame[2]
                if channel_group == 0x00:  # All channels
                    expected_frames = 3
                    expected_bytes = 17
                else:  # Single group
                    expected_frames = 2
                    expected_bytes = 10
            else:
                return None, None
        else:
            expected_frames = protocol['frames']
            expected_bytes = protocol['total_bytes']
        
        # Check if we have enough frames
        if len(self.pending_frames) < expected_frames:
            return None, None
            
        # Combine all frame data
        complete_data = []
        for frame in self.pending_frames[:expected_frames]:
            complete_data.extend(frame)
            
        # Verify total length
        if len(complete_data) >= expected_bytes:
            # Clear pending frames and return complete message
            self.pending_frames = []
            return protocol['name'], complete_data[:expected_bytes]
            
        return None, None
    
    def clear(self):
        """Clear all pending frames"""
        self.pending_frames = []