('D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\RF_Module_Test.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('main',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\main.py',
   'PYSOURCE')],
 'python312.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
