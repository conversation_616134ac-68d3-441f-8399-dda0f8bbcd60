# RF模块测试上位机 V0.1

## 概述
本程序用于控制和测试基于STM32F107的射频模块，通过CAN总线进行通信。

## 运行要求
- Python 3.7+
- PyQt5
- Windows系统（由于使用了ZLG的Windows DLL）
- ZLG USBCAN-II 设备

## 安装与运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行程序：
```bash
# 使用批处理文件（推荐）
run.bat

# 或直接运行
python main.py
```

## V0.1 新特性

### 1. 快捷步进按钮
- 频率调节：`-10` `-1` `+1` `+10` MHz
- 射频衰减：`-5` `+5` dB

### 2. 鼠标滚轮控制
- 点击数值框获取焦点
- 滚轮上下调节数值
- `Ctrl+滚轮`：10倍步进
- `Shift+滚轮`：0.1倍步进

### 3. 自动发送
- 勾选"自动发送"后，参数改变自动应用
- 500ms延迟防抖，避免频繁发送

### 4. 视觉反馈
- 发送按钮加粗显示
- 发送成功时按钮短暂变绿

## 使用说明

1. **连接设备**
   - 选择CAN通道（0或1）
   - 点击"连接"按钮

2. **查询功能**
   - 查询模块信息
   - 查询BIT状态
   - 查询通道参数

3. **参数设置**
   - 使用快捷按钮或滚轮调节参数
   - 点击"发送"或启用"自动发送"

4. **通道分组**
   - 所有通道（1-10）
   - 通道1-5
   - 通道6-10

## 故障排除

1. **连接失败**
   - 检查USBCAN-II设备是否正确连接
   - 确认驱动已安装
   - 检查CAN总线连接

2. **DLL加载错误**
   - 使用run.bat启动程序
   - 确保所有DLL文件在正确位置

3. **通信超时**
   - 检查CAN总线波特率（500kbps）
   - 确认目标设备已开启

## 技术支持
如有问题，请查看日志标签页中的错误信息。