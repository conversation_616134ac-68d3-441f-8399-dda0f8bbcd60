"""
DLL路径设置模块
在程序启动时设置正确的DLL搜索路径
"""

import os
import sys

def setup_dll_paths():
    """设置DLL搜索路径"""
    # 获取程序运行目录
    if getattr(sys, 'frozen', False):
        # 打包后的exe
        application_path = os.path.dirname(sys.executable)
    else:
        # 开发环境
        application_path = os.path.dirname(os.path.abspath(__file__))
    
    # 添加到PATH
    os.environ['PATH'] = application_path + os.pathsep + os.environ.get('PATH', '')
    
    # 添加kerneldlls目录
    kerneldlls_path = os.path.join(application_path, 'kerneldlls')
    if os.path.exists(kerneldlls_path):
        os.environ['PATH'] = kerneldlls_path + os.pathsep + os.environ.get('PATH', '')
        
        # 添加子目录
        for subdir in ['ZPS']:
            subpath = os.path.join(kerneldlls_path, subdir)
            if os.path.exists(subpath):
                os.environ['PATH'] = subpath + os.pathsep + os.environ.get('PATH', '')
    
    # Windows特定设置
    if sys.platform == 'win32':
        try:
            import ctypes
            # 设置DLL目录
            ctypes.windll.kernel32.SetDllDirectoryW(application_path)
            # 预加载关键DLL
            zlgcan_path = os.path.join(application_path, 'zlgcan.dll')
            if os.path.exists(zlgcan_path):
                ctypes.windll.LoadLibrary(zlgcan_path)
        except:
            pass
    
    return application_path