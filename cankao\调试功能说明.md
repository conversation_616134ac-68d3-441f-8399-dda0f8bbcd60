# V0.1 调试功能说明

## 增强的日志记录功能

V0.1版本在发送参数时会同时记录两种信息，方便调试和验证：

### 1. 日志标签页（原始数据）
显示CAN通信的底层细节：
- **TX Frame**: 发送的原始十六进制数据
- **编码详情**: 数据转换过程中的关键值
- **RX**: 接收到的原始数据

示例：
```
[PROTOCOL] TX Frame 1: F0 04 00 12 00 01 86 A0
[PROTOCOL] TX Frame 2: 02 05
[PROTOCOL] 编码详情: freq_khz=100000, rf_att_val=2, work_mode=0, control_byte=0x12
[CAN] RX: F0 00 01 20 24 12 31 ...
```

### 2. 状态标签页（人类可读）
显示设置的实际参数值：
```
[14:25:30] 设置参数 - 所有通道
  频率: 100.000 MHz
  射频衰减: 10 dB
  中频衰减: 5 dB
  带宽: 5 MHz
  电源: 开启
```

## 数据编码对照表

### 控制字节 (control_byte) 编码
```
Bit 7-5: 保留
Bit 4:   带宽有效位 (1=有效)
Bit 3-2: 保留
Bit 1-0: 带宽值 (00=200kHz, 01=5MHz, 10=50MHz) | 工作模式 (0=开, 1=关)
```

### 频率编码
- 单位转换：MHz → kHz (×1000)
- 字节序：大端 (Big-Endian)
- 示例：100 MHz = 100000 kHz = 0x000186A0

### 衰减编码
- 射频衰减：dB值 ÷ 5 = 编码值
  - 0 dB → 0x00
  - 10 dB → 0x02
  - 20 dB → 0x04
  - 35 dB → 0x07
- 中频衰减：直接使用dB值

## 调试技巧

### 1. 验证参数编码
在日志中查看"编码详情"行，确认：
- `freq_khz`: 频率是否正确转换为kHz
- `rf_att_val`: 射频衰减是否正确除以5
- `work_mode`: 电源状态是否正确（0=开，1=关）
- `control_byte`: 控制字节是否正确组合

### 2. 跟踪通信流程
1. 查看"TX Frame"确认发送的数据
2. 查看"RX"确认设备响应
3. 对照协议文档验证数据格式

### 3. 快速定位问题
- **参数设置失败**：检查日志中的验证错误信息
- **通信超时**：查看是否有RX响应
- **数据错误**：对比状态显示和日志中的原始数据

## 示例：调试一次参数设置

设置：频率=2450.5 MHz, RF衰减=20 dB, IF衰减=15 dB

**状态显示**：
```
设置参数 - 通道 1-5
  频率: 2450.500 MHz
  射频衰减: 20 dB
  中频衰减: 15 dB
  带宽: 50 MHz
  电源: 关闭
```

**日志显示**：
```
TX Frame 1: F0 04 01 15 00 25 64 44
            │  │  │  │  └─ 频率 2450500 kHz (大端)
            │  │  │  └─ 控制字节 (带宽50MHz+电源关)
            │  │  └─ 通道组 01 (通道1-5)
            │  └─ 命令 04 (设置参数)
            └─ 命令头 F0

TX Frame 2: 04 0F
            │  └─ IF衰减 15 dB
            └─ RF衰减值 4 (20÷5)
```

这样的双重日志记录让调试变得更加方便和直观。