"""
CAN Message Queue for handling received messages
Provides thread-safe message queueing between receive thread and protocol handlers
"""

import threading
import time
from collections import deque


class CANMessageQueue:
    """Thread-safe message queue for CAN messages"""
    
    def __init__(self):
        self.queue = deque()
        self.lock = threading.Lock()
        self.event = threading.Event()
        
    def put(self, message):
        """Add a message to the queue
        
        Args:
            message: List of bytes representing the CAN message
        """
        with self.lock:
            self.queue.append(message)
            self.event.set()
            
    def get(self, timeout=1.0):
        """Get a message from the queue
        
        Args:
            timeout: Maximum time to wait in seconds
            
        Returns:
            List of bytes or None if timeout
        """
        end_time = time.time() + timeout
        
        while time.time() < end_time:
            with self.lock:
                if self.queue:
                    # Only clear event if queue is now empty
                    msg = self.queue.popleft()
                    if not self.queue:
                        self.event.clear()
                    return msg
                    
            # Wait for new message
            remaining = end_time - time.time()
            if remaining > 0:
                self.event.wait(min(remaining, 0.1))
                # Don't clear here - let the producer control the event
            else:
                break
                
        return None
        
    def get_multiple(self, count, timeout=3.0):
        """Get multiple messages from the queue
        
        Args:
            count: Number of messages expected
            timeout: Maximum time to wait for all messages
            
        Returns:
            List of messages or None if timeout
        """
        messages = []
        end_time = time.time() + timeout
        
        while len(messages) < count and time.time() < end_time:
            remaining = end_time - time.time()
            msg = self.get(timeout=remaining)
            if msg:
                messages.append(msg)
            else:
                break
                
        return messages if len(messages) == count else messages
        
    def clear(self):
        """Clear all messages from the queue"""
        with self.lock:
            self.queue.clear()
            
    def is_empty(self):
        """Check if queue is empty"""
        with self.lock:
            return len(self.queue) == 0