(['D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\main.py'],
 ['D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2'],
 ['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets'],
 [('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [('ControlCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\ControlCAN.dll',
   'BINARY'),
  ('zlgcan.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\zlgcan.dll',
   'BINARY')],
 [('kerneldlls\\CANDTU_NET.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDTU_NET.dll',
   'DATA'),
  ('kerneldlls\\CANDTU_x64.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDTU_x64.dll',
   'DATA'),
  ('kerneldlls\\CANDevCore.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDevCore.dll',
   'DATA'),
  ('kerneldlls\\CANDevice.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDevice.dll',
   'DATA'),
  ('kerneldlls\\CANETE.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANETE.dll',
   'DATA'),
  ('kerneldlls\\CANET_TCP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANET_TCP.dll',
   'DATA'),
  ('kerneldlls\\CANFDCOM.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANFDCOM.dll',
   'DATA'),
  ('kerneldlls\\CANFDDTU.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANFDDTU.dll',
   'DATA'),
  ('kerneldlls\\CANFDNET.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANFDNET.dll',
   'DATA'),
  ('kerneldlls\\CANWIFI_TCP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANWIFI_TCP.dll',
   'DATA'),
  ('kerneldlls\\CANWIFI_UDP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANWIFI_UDP.dll',
   'DATA'),
  ('kerneldlls\\USBCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCAN.dll',
   'DATA'),
  ('kerneldlls\\USBCAN.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCAN.xml',
   'DATA'),
  ('kerneldlls\\USBCANFD.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCANFD.dll',
   'DATA'),
  ('kerneldlls\\USBCANFD800U.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCANFD800U.dll',
   'DATA'),
  ('kerneldlls\\VCI_USBCAN2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\VCI_USBCAN2.xml',
   'DATA'),
  ('kerneldlls\\VirtualUSBCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\VirtualUSBCAN.dll',
   'DATA'),
  ('kerneldlls\\ZPSCANFD.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPSCANFD.dll',
   'DATA'),
  ('kerneldlls\\ZPS\\ZPSCANFD_IMPL.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\ZPSCANFD_IMPL.dll',
   'DATA'),
  ('kerneldlls\\ZPS\\base.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\base.dll',
   'DATA'),
  ('kerneldlls\\ZPS\\dataset.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\dataset.dll',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\aio_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\aio_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\async_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\async_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\can_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\can_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\devicevartype.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\devicevartype.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dio_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\dio_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dio_module.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\dio_module.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dso_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\dso_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\import_initer.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\import_initer.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\imports.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\imports.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\receivedisturb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\receivedisturb.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\send_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\send_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dataset\\dataset_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\dataset\\dataset_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.ztdev',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.ztdev',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\cpp_types.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\cpp_types.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\project_mgr.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\project_mgr.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\senddata_manager.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\senddata_manager.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\system.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\system.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\system_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\system_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\zps_device_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\zps_device_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\unit\\eye\\eye.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\unit\\eye\\eye.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\zps\\zps_cpp_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\zps\\zps_cpp_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\sys_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\sys_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\type_attrs.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\type_attrs.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\wave_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\wave_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\zps_comm.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\zps_comm.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\zps_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\zps_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\function\\canfilter\\canfilter.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\function\\canfilter\\canfilter.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\function\\decode\\canwavedecode.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\function\\decode\\canwavedecode.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\fft_reference_wave_edit_dlg.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\fft_reference_wave_edit_dlg.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\func_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\func_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\math_func_mgr.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\math_func_mgr.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\tds_func_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\tds_func_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\test_fun_dialog.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\test_fun_dialog.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\upload_params_view.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\upload_params_view.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\user_input_config_view.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\user_input_config_view.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\user_input_param_dialog.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\user_input_param_dialog.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\zcomm_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\zcomm_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\ui\\default_ui_types.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\ui\\default_ui_types.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\ui\\default_widget_property.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\ui\\default_widget_property.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\utils.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\utils.dll',
   'DATA'),
  ('kerneldlls\\ZPS\\z_comm_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\z_comm_1.0.0.zdm',
   'DATA'),
  ('kerneldlls\\ZPS\\zps_device_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\zps_device_1.0.0.zdm',
   'DATA'),
  ('kerneldlls\\ZPS\\zps_function_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\zps_function_1.0.0.zdm',
   'DATA'),
  ('kerneldlls\\ZlgCloud.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZlgCloud.dll',
   'DATA'),
  ('kerneldlls\\devices_property\\can.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\can.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-100ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-100ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-200ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-200ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-net-400.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-net-400.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-net.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-net.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu.ini',
   'DATA'),
  ('kerneldlls\\devices_property\\canet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdblue-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdblue-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdbridgeplus.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdbridgeplus.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdcom-100ie.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdcom-100ie.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfddtu-200.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfddtu-200.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfddtu-300.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfddtu-300.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet100-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet100-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet100-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet100-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet30cascade-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet30cascade-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet30cascade-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet30cascade-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet400u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet400u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet400u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet400u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet600u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet600u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet600u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet600u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet800u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet800u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet800u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet800u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdwifi-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdwifi-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdwifi-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdwifi-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canscope.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canscope.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\can.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\can.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-100ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-100ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-200ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-200ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-net-400.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-net-400.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-net.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-net.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu.ini',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdblue-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdblue-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdbridgeplus.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdbridgeplus.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdcom-100ie.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdcom-100ie.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfddtu-200.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfddtu-200.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfddtu-300.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfddtu-300.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet100-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet100-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet100-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet100-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet30cascade-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet30cascade-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet30cascade-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet30cascade-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet400u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet400u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet400u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet400u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet600u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet600u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet600u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet600u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet800u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet800u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet800u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet800u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdwifi-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdwifi-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdwifi-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdwifi-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canscope.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canscope.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\device_locale_strings.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\device_locale_strings.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pci-5010-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pci-5010-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pci-5020-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pci-5020-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-100u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-100u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-200u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-200u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-400u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-400u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-2e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-2e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-4e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-4e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-8e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-8e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan1.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan1.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan2.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan4.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan4.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-800h.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-800h.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\virtual.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\virtual.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\zpscanfd-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\zpscanfd-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\zpscanfd-usb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\zpscanfd-usb.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\device_locale_strings.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\device_locale_strings.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\mini-pcie-canfd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\mini-pcie-canfd.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pci-5010-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pci-5010-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pci-5020-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pci-5020-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-100u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-100u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-1200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-1200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-200u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-200u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-400u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-400u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-2e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-2e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-4e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-4e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-8e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-8e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan1.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan1.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan2.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan4.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan4.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-800h.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-800h.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\virtual.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\virtual.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\zpscanfd-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\zpscanfd-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\zpscanfd-usb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\zpscanfd-usb.xml',
   'DATA'),
  ('kerneldlls\\dll_cfg.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\dll_cfg.ini',
   'DATA')],
 '3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 12:21:36) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('main',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\main.py',
   'PYSOURCE')],
 [('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipimport.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tempfile.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\copy.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\struct.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bisect.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tarfile.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\textwrap.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gettext.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\quopri.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\csv.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\token.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pathlib.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\inspect.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ast.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\signal.py',
   'PYMODULE'),
  ('main_window',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\main_window.py',
   'PYMODULE'),
  ('can_frame_assembler',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\can_frame_assembler.py',
   'PYMODULE'),
  ('can_message_queue',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\can_message_queue.py',
   'PYMODULE'),
  ('rf_module_protocol',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\rf_module_protocol.py',
   'PYMODULE'),
  ('usbcan2_interface',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\usbcan2_interface.py',
   'PYMODULE'),
  ('zlgcan',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\zlgcan.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\platform.py',
   'PYMODULE'),
  ('channel_control_widget',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\channel_control_widget.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dll_loader',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\dll_loader.py',
   'PYMODULE')],
 [('kerneldlls\\CANDTU_NET.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDTU_NET.dll',
   'BINARY'),
  ('kerneldlls\\CANDTU_x64.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDTU_x64.dll',
   'BINARY'),
  ('kerneldlls\\CANDevCore.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDevCore.dll',
   'BINARY'),
  ('kerneldlls\\CANDevice.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANDevice.dll',
   'BINARY'),
  ('kerneldlls\\CANETE.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANETE.dll',
   'BINARY'),
  ('kerneldlls\\CANET_TCP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANET_TCP.dll',
   'BINARY'),
  ('kerneldlls\\CANFDCOM.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANFDCOM.dll',
   'BINARY'),
  ('kerneldlls\\CANFDDTU.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANFDDTU.dll',
   'BINARY'),
  ('kerneldlls\\CANFDNET.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANFDNET.dll',
   'BINARY'),
  ('kerneldlls\\CANWIFI_TCP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANWIFI_TCP.dll',
   'BINARY'),
  ('kerneldlls\\CANWIFI_UDP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\CANWIFI_UDP.dll',
   'BINARY'),
  ('kerneldlls\\USBCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCAN.dll',
   'BINARY'),
  ('kerneldlls\\USBCANFD.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCANFD.dll',
   'BINARY'),
  ('kerneldlls\\USBCANFD800U.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCANFD800U.dll',
   'BINARY'),
  ('kerneldlls\\VirtualUSBCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\VirtualUSBCAN.dll',
   'BINARY'),
  ('kerneldlls\\ZPSCANFD.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPSCANFD.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\ZPSCANFD_IMPL.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\ZPSCANFD_IMPL.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\base.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\base.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\dataset.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\dataset.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\utils.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\utils.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\z_comm_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\z_comm_1.0.0.zdm',
   'BINARY'),
  ('kerneldlls\\ZPS\\zps_device_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\zps_device_1.0.0.zdm',
   'BINARY'),
  ('kerneldlls\\ZPS\\zps_function_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\zps_function_1.0.0.zdm',
   'BINARY'),
  ('kerneldlls\\ZlgCloud.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZlgCloud.dll',
   'BINARY'),
  ('ControlCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\ControlCAN.dll',
   'BINARY'),
  ('zlgcan.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\zlgcan.dll',
   'BINARY'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('MSVCP120.dll', 'C:\\WINDOWS\\system32\\MSVCP120.dll', 'BINARY'),
  ('MSVCR120.dll', 'C:\\WINDOWS\\system32\\MSVCR120.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('base.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\base.dll',
   'BINARY'),
  ('utils.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\utils.dll',
   'BINARY'),
  ('dataset.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\dataset.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY')],
 [],
 [],
 [('kerneldlls\\USBCAN.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\USBCAN.xml',
   'DATA'),
  ('kerneldlls\\VCI_USBCAN2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\VCI_USBCAN2.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\aio_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\aio_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\async_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\async_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\can_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\can_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\devicevartype.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\devicevartype.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dio_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\dio_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dio_module.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\dio_module.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dso_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\dso_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\import_initer.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\import_initer.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\imports.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\imports.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\receivedisturb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\receivedisturb.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\send_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\send_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dataset\\dataset_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\dataset\\dataset_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.ztdev',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.ztdev',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\cpp_types.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\cpp_types.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\project_mgr.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\project_mgr.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\senddata_manager.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\senddata_manager.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\system.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\system.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\system_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\system_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\zps_device_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\system\\zps_device_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\unit\\eye\\eye.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\unit\\eye\\eye.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\zps\\zps_cpp_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\source\\zps\\zps_cpp_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\sys_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\sys_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\type_attrs.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\type_attrs.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\wave_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\wave_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\zps_comm.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\zps_comm.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\zps_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\device\\zps_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\function\\canfilter\\canfilter.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\function\\canfilter\\canfilter.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\function\\decode\\canwavedecode.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\resources\\function\\decode\\canwavedecode.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\fft_reference_wave_edit_dlg.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\fft_reference_wave_edit_dlg.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\func_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\func_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\math_func_mgr.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\math_func_mgr.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\tds_func_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\tds_func_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\test_fun_dialog.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\test_fun_dialog.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\upload_params_view.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\upload_params_view.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\user_input_config_view.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\user_input_config_view.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\user_input_param_dialog.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\user_input_param_dialog.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\zcomm_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\base\\zcomm_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\ui\\default_ui_types.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\ui\\default_ui_types.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\ui\\default_widget_property.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\ZPS\\source\\zview\\ui\\default_widget_property.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\can.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\can.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-100ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-100ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-200ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-200ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-net-400.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-net-400.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-net.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu-net.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\candtu.ini',
   'DATA'),
  ('kerneldlls\\devices_property\\canet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdblue-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdblue-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdbridgeplus.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdbridgeplus.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdcom-100ie.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdcom-100ie.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfddtu-200.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfddtu-200.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfddtu-300.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfddtu-300.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet100-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet100-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet100-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet100-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet30cascade-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet30cascade-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet30cascade-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet30cascade-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet400u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet400u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet400u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet400u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet600u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet600u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet600u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet600u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet800u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet800u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet800u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdnet800u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdwifi-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdwifi-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdwifi-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canfdwifi-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canscope.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\canscope.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\can.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\can.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-100ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-100ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-200ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-200ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-net-400.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-net-400.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-net.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu-net.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\candtu.ini',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdblue-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdblue-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdbridgeplus.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdbridgeplus.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdcom-100ie.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdcom-100ie.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfddtu-200.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfddtu-200.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfddtu-300.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfddtu-300.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet100-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet100-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet100-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet100-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet30cascade-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet30cascade-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet30cascade-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet30cascade-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet400u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet400u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet400u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet400u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet600u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet600u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet600u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet600u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet800u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet800u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet800u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdnet800u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdwifi-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdwifi-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdwifi-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canfdwifi-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canscope.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\canscope.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\device_locale_strings.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\device_locale_strings.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pci-5010-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pci-5010-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pci-5020-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pci-5020-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-100u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-100u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-200u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-200u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-400u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-400u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\pcie-canfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-2e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-2e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-4e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-4e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-8e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-8e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan-e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan1.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan1.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan2.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan4.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcan4.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-800h.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-800h.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\usbcanfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\virtual.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\virtual.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\zpscanfd-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\zpscanfd-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\zpscanfd-usb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\default\\zpscanfd-usb.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\device_locale_strings.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\device_locale_strings.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\mini-pcie-canfd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\mini-pcie-canfd.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pci-5010-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pci-5010-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pci-5020-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pci-5020-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-100u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-100u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-1200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-1200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-200u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-200u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-400u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-400u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\pcie-canfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-2e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-2e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-4e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-4e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-8e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-8e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan-e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan1.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan1.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan2.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan4.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcan4.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-800h.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-800h.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\usbcanfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\virtual.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\virtual.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\zpscanfd-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\zpscanfd-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\zpscanfd-usb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\devices_property\\zpscanfd-usb.xml',
   'DATA'),
  ('kerneldlls\\dll_cfg.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\kerneldlls\\dll_cfg.ini',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.2\\build\\normal_pack\\base_library.zip',
   'DATA')],
 [('sre_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\codecs.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\stat.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\functools.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ntpath.py',
   'PYMODULE'),
  ('enum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\enum.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\traceback.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\warnings.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\linecache.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\abc.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\locale.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\copyreg.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\posixpath.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\weakref.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\operator.py',
   'PYMODULE'),
  ('io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\io.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\keyword.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\os.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\genericpath.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\types.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\reprlib.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\heapq.py',
   'PYMODULE')])
