#### 1. 数据帧结构

数据帧由消息类别、消息ID以及数据部分组成，具体格式如下：

| 字节            | 数据位       | 信号描述 |
| ------------- | --------- | ---- |
| BYTE0         | BIT7-BIT0 | 消息类别 |
| BYTE1         | BIT7-BIT0 | 消息ID |
| BYTE2~BYTE255 | BIT7-BIT0 | 数据区  |

#### 2. CAN总线控制命令

##### 2.1 参数设置

根据需求，主要的控制包括两个通道的信号开关、工作模式、频率以及衰减等，具体的设置格式如下：

| Byte  | Bit       | 说明        | 值                          |
| ----- | --------- | --------- | -------------------------- |
| 0     | BIT7-BIT0 | 消息类别      | 0xF0                       |
| 1     |           | 消息ID      | 0x04                       |
| 2     |           | 通道 1 信号关断 | 0-关, 1-开                   |
| 3     |           | 通道 1 工作模式 | 0-单点模式, 1-扫频               |
| 4~7   |           | 通道 1 频率   | 2000~18000MHz (1M步进, 无符号整型) |
| 8     |           | 通道 1 衰减   | 0~50dB 5dB步进               |
| 9     |           | 保留        | 校准用0x00                    |
| 10    |           | 通道 2 信号关断 | 0-关, 1-开                   |
| 11    |           | 通道 2 工作模式 | 0-单点模式, 1-扫频               |
| 12~15 |           | 通道 2 频率   | 30~3000MHz (1M步进, 无符号整型)   |
| 16    |           | 通道 2 衰减   | 0~50dB 5dB步进               |
| 17    |           | 保留        | 校准用0x00                    |
| 18    |           | 保留        | 0x00                       |
| 19    |           | 保留        | 0x00                       |

##### 2.2 模块BIT查询

查询模块的电压、电流、功率和温度等状态信息。

**查询命令格式：**

| Byte | Bit       | 说明     | 值    |
| ---- | --------- | ------ | ---- |
| 0    | BIT7-BIT0 | 消息类别   | 0xF0 |
| 1    | BIT7-BIT0 | 消息ID   | 0x01 |

**查询响应格式：**

| Byte  | Bit       | 说明                                           | 值                        |
| ----- | --------- | -------------------------------------------- | ------------------------ |
| 0     | BIT7-BIT0 | 消息类别                                         | 0xF0                     |
| 1     | BIT7-BIT0 | 消息ID                                         | 0x01                     |
| 2     | BIT7-BIT0 | 当前温度检测值<br>取值范围：[-55~125]<br>单位：℃<br>数据类型：有符号整型 | -55~125                  |
| 3~4   | BIT7-BIT0 | 输入电压（VIN）<br>单位：0.1V<br>精度：0.1V<br>数据类型：无符号整型  | 高字节在前，低字节在后              |
| 5~6   | BIT7-BIT0 | 电流值<br>单位：0.001A（mA）<br>精度：1mA<br>数据类型：无符号整型  | 高字节在前，低字节在后              |
| 7~8   | BIT7-BIT0 | 功率值<br>单位：0.01W（10mW）<br>精度：10mW<br>数据类型：无符号整型 | 高字节在前，低字节在后              |
| 9~19  | BIT7-BIT0 | 保留                                           | 0x00                     |
