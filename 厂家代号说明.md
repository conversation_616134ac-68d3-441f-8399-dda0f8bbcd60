# 厂家代号说明

## 代号映射表

| 代号 | 厂家名称 | 说明 |
|------|----------|------|
| 0    | 十所     | 中国电子科技集团公司第十研究所 |
| 1-255| 数字显示 | 其他厂家使用数字代号 |

## 显示规则

### 1. 查询显示
- **状态标签页**: 显示为 "十所 (0)" 或具体数字
- **当前值标签**: 显示为 "厂家十所" 或 "厂家X"

### 2. 写入显示
- 日志中显示为 "十所 (0)" 格式，方便对照

### 3. 输入控件
- 输入框仍使用数字输入（0-255）
- 用户需要知道0代表十所

## 使用示例

```
查询结果显示：
- 制造商代码: 十所 (0)
- 当前值: 厂家十所, 出厂2025-01-03, 序列号2025-07-001

写入日志显示：
- 厂家代号: 十所 (0)
```

## 扩展说明

如需添加更多厂家代号映射，可以修改以下代码位置：

1. `main_window.py` 第515行和第503行
2. `rf_module_protocol.py` 第505行

建议使用字典进行映射管理，便于后续维护。