# V0.1 界面布局说明

## 整体布局结构

```
┌─────────────────────────────────────────────────────────────┐
│                        连接设置                                │
├───────────────────────────┬─────────────────────────────────┤
│                           │      产品信息设置（调试功能）        │
│   所有通道 (1-10)          │  ┌─────────────────────────────┐ │
│   ┌─────────────────┐     │  │ 厂家代号 [  ] 出厂日期 [   ] │ │
│   │  频率/衰减/带宽   │     │  │ 序列号 [    ]-[  ]-[   ]     │ │
│   │  自动发送 [手动]  │     │  │ 当前值: ...    [读取] [写入] │ │
│   └─────────────────┘     │  └─────────────────────────────┘ │
│                           ├─────────────────────────────────┤
│   通道 1-5                 │                                 │
│   ┌─────────────────┐     │      ┌──────┬──────┐           │
│   │  频率/衰减/带宽   │     │      │ 日志  │ 状态  │           │
│   │  自动发送 [手动]  │     │      └──────┴──────┘           │
│   └─────────────────┘     │      ┌─────────────────┐        │
│                           │      │                   │        │
│   通道 6-10                │      │  日志/状态显示区   │        │
│   ┌─────────────────┐     │      │                   │        │
│   │  频率/衰减/带宽   │     │      │                   │        │
│   │  自动发送 [手动]  │     │      └─────────────────┘        │
│   └─────────────────┘     │                                 │
│                           │                                 │
│   查询操作                 │                                 │
│   ┌─────────────────┐     │                                 │
│   │ 模块信息 BIT状态 │     │                                 │
│   │ 所有通道 1-5 6-10│     │                                 │
│   │         清除日志  │     │                                 │
│   └─────────────────┘     │                                 │
└───────────────────────────┴─────────────────────────────────┘
```

## 布局调整说明 (V0.1)

### 1. 左侧控制区域
- **顶部**: 通道控制（所有通道、通道1-5、通道6-10）
- **底部**: 查询操作按钮组
- **调整原因**: 常用的控制功能放在上方，更符合操作习惯

### 2. 右侧显示区域
- **顶部**: 产品信息设置（高度150px）
- **底部**: 日志/状态标签页（占主要空间）
- **调整原因**: 产品信息设置使用频率较低，放在上方不影响日志查看

### 3. 细节优化
- **列间距**: 从5px增加到8px，界面更舒适
- **产品信息布局**: 紧凑设计，序列号使用"年份-批次-序号"格式
- **自动功能**: 连接后500ms自动读取产品信息

## 操作流程优化

1. **连接设备** → 自动读取产品信息
2. **参数控制** → 直接在顶部操作，自动发送
3. **查询功能** → 在控制区下方，需要时使用
4. **日志查看** → 主要显示区域，方便监控

## 分割比例
- 左右分割: 500:700 (控制区:显示区)
- 上下分割: 150:450 (产品信息:日志状态)

这种布局更符合实际使用习惯，将最常用的功能放在最方便的位置。