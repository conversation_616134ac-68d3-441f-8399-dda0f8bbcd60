"""
RF Module CAN Protocol Implementation - V2.0
Implements the simplified 2-channel CAN protocol for STM32F107-based RF modules
Based on new Doc/CAN通信协议.md
"""

import struct
import time
from datetime import datetime


class RFModuleProtocol:
    """RF Module protocol handler for 2-channel system"""
    
    # Command IDs
    CMD_SET_PARAMS = [0xF0, 0x04]
    CMD_QUERY_BIT = [0xF0, 0x01]  # BIT查询命令
    
    # Channel definitions
    CHANNEL_1 = 1
    CHANNEL_2 = 2
    
    # Work modes
    MODE_SINGLE_POINT = 0  # 单点模式  
    MODE_SWEEP = 1         # 扫频
    
    # Signal control
    SIGNAL_OFF = 0         # 信号关断
    SIGNAL_ON = 1          # 信号开启
    
    def __init__(self, can_interface):
        self.can = can_interface
        self.log_callback = None
        self.status_log_callback = None
        
    def set_log_callback(self, callback):
        """Set log callback for debug output"""
        self.log_callback = callback
        
    def set_status_log_callback(self, callback):
        """Set status log callback for human-readable status display"""
        self.status_log_callback = callback
        
    def _log(self, message):
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(f"[Protocol] {message}")
        
    def set_channel_params(self, channel1_params, channel2_params):
        """Set parameters for both channels
        
        Args:
            channel1_params: dict with keys: signal_enable, work_mode, frequency, attenuation
            channel2_params: dict with keys: signal_enable, work_mode, frequency, attenuation
            
        Expected format:
        - signal_enable: 0 (off) or 1 (on)
        - work_mode: 0 (single point) or 1 (sweep)
        - frequency: frequency in MHz
        - attenuation: attenuation in dB (0-50, 5dB steps)
            
        Returns:
            bool: True if successful
        """
        # Log the human-readable parameters
        if self.status_log_callback:
            status_msg = "设置参数\n"
            status_msg += f"通道1: {channel1_params['frequency']:.0f}MHz, "
            status_msg += f"衰减{channel1_params['attenuation']}dB, "
            status_msg += f"{'开启' if channel1_params['signal_enable'] else '关闭'}, "
            status_msg += f"{'扫频' if channel1_params['work_mode'] else '单点'}\n"
            status_msg += f"通道2: {channel2_params['frequency']:.0f}MHz, "
            status_msg += f"衰减{channel2_params['attenuation']}dB, "
            status_msg += f"{'开启' if channel2_params['signal_enable'] else '关闭'}, "
            status_msg += f"{'扫频' if channel2_params['work_mode'] else '单点'}"
            self.status_log_callback(status_msg)
        
        # Validate channel 1 parameters
        if not self._validate_channel1_params(channel1_params):
            return False
            
        # Validate channel 2 parameters  
        if not self._validate_channel2_params(channel2_params):
            return False
        
        # Build 20-byte CAN frame according to new protocol
        frame = [0] * 20
        
        # Header
        frame[0] = 0xF0  # Message category
        frame[1] = 0x04  # Message ID
        
        # Channel 1 parameters (bytes 2-9)
        frame[2] = channel1_params['signal_enable'] & 0x01
        frame[3] = channel1_params['work_mode'] & 0x01
        freq1_mhz = int(channel1_params['frequency'])
        frame[4] = (freq1_mhz >> 24) & 0xFF
        frame[5] = (freq1_mhz >> 16) & 0xFF  
        frame[6] = (freq1_mhz >> 8) & 0xFF
        frame[7] = freq1_mhz & 0xFF
        frame[8] = int(channel1_params['attenuation']) & 0xFF
        frame[9] = 0x00  # Reserved for calibration
        
        # Channel 2 parameters (bytes 10-17)
        frame[10] = channel2_params['signal_enable'] & 0x01
        frame[11] = channel2_params['work_mode'] & 0x01
        freq2_mhz = int(channel2_params['frequency'])
        frame[12] = (freq2_mhz >> 24) & 0xFF
        frame[13] = (freq2_mhz >> 16) & 0xFF
        frame[14] = (freq2_mhz >> 8) & 0xFF
        frame[15] = freq2_mhz & 0xFF
        frame[16] = int(channel2_params['attenuation']) & 0xFF
        frame[17] = 0x00  # Reserved for calibration
        
        # Reserved bytes (18-19)
        frame[18] = 0x00
        frame[19] = 0x00
        
        # Log the raw hex data
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Log detailed encoding
        self._log(f"编码详情: CH1 freq={freq1_mhz}MHz, att={channel1_params['attenuation']}dB, "
                 f"CH2 freq={freq2_mhz}MHz, att={channel2_params['attenuation']}dB")
        
        # Clear receive buffer first (reduced timeout for speed)
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send 20-byte frame using multi-frame method (3 frames: 8+8+4 bytes)
        if not self.can.send_multi_frame(frame):
            self._log("Failed to send parameter frame")
            return False
            
        # Optimized: Don't wait for confirmation to improve response speed
        # Most embedded systems don't send confirmation for parameter setting
        self._log("Parameter frame sent successfully")
        return True
        
    def _validate_channel1_params(self, params):
        """Validate channel 1 parameters"""
        # Channel 1 frequency range: 2000-18000 MHz
        freq = params['frequency']
        if not (2000 <= freq <= 18000):
            self._log(f"Invalid channel 1 frequency: {freq} MHz (range: 2000-18000)")
            return False
            
        # Attenuation: 0-50 dB, 5dB steps
        att = params['attenuation']
        if att % 5 != 0 or not (0 <= att <= 50):
            self._log(f"Invalid channel 1 attenuation: {att} dB (0-50, 5dB steps)")
            return False
            
        # Signal enable: 0 or 1
        if params['signal_enable'] not in [0, 1]:
            self._log(f"Invalid channel 1 signal enable: {params['signal_enable']}")
            return False
            
        # Work mode: 0 or 1
        if params['work_mode'] not in [0, 1]:
            self._log(f"Invalid channel 1 work mode: {params['work_mode']}")
            return False
            
        return True
        
    def _validate_channel2_params(self, params):
        """Validate channel 2 parameters"""
        # Channel 2 frequency range: 30-3000 MHz
        freq = params['frequency']
        if not (30 <= freq <= 3000):
            self._log(f"Invalid channel 2 frequency: {freq} MHz (range: 30-3000)")
            return False
            
        # Attenuation: 0-50 dB, 5dB steps
        att = params['attenuation']
        if att % 5 != 0 or not (0 <= att <= 50):
            self._log(f"Invalid channel 2 attenuation: {att} dB (0-50, 5dB steps)")
            return False
            
        # Signal enable: 0 or 1
        if params['signal_enable'] not in [0, 1]:
            self._log(f"Invalid channel 2 signal enable: {params['signal_enable']}")
            return False
            
        # Work mode: 0 or 1  
        if params['work_mode'] not in [0, 1]:
            self._log(f"Invalid channel 2 work mode: {params['work_mode']}")
            return False
            
        return True

    def get_frequency_range(self, channel):
        """Get frequency range for specified channel
        
        Args:
            channel: 1 or 2
            
        Returns:
            tuple: (min_freq, max_freq) in MHz
        """
        if channel == 1:
            return (2000, 18000)
        elif channel == 2:
            return (30, 3000)
        else:
            raise ValueError(f"Invalid channel: {channel}")

    def get_attenuation_range(self):
        """Get attenuation range for both channels
        
        Returns:
            tuple: (min_att, max_att, step) in dB
        """
        return (0, 50, 5)
    
    def query_bit_info(self):
        """Query BIT (Built-In Test) information from module
        
        Sends BIT query command and waits for response
        
        Returns:
            dict: BIT information or None if failed
            {
                'temperature': float,  # Temperature in Celsius
                'voltage': float,      # Input voltage in V
                'current': float,      # Current in A
                'power': float         # Power in W
            }
        """
        # Build query command frame
        frame = [0xF0, 0x01]  # Message category + ID
        
        # Log query
        self._log("Sending BIT query command")
        hex_str = ' '.join(f'{b:02X}' for b in frame)
        self._log(f"TX Frame: {hex_str}")
        
        # Clear receive buffer before sending
        if hasattr(self.can, 'clear_receive_buffer'):
            self.can.clear_receive_buffer()
        
        # Send query frame
        if not self.can.send_message(frame):
            self._log("Failed to send BIT query command")
            return None
        
        # Wait for response (with timeout)
        start_time = time.time()
        timeout = 2.0  # Increased to 2 seconds for slower responses
        received_data = []
        frame_count = 0
        
        # Add initial delay to allow MCU processing time
        time.sleep(0.15)  # 150ms initial delay for MCU to process and start responding
        
        while time.time() - start_time < timeout:
            # Try to receive messages with longer timeout
            messages = self.can.receive_messages(max_messages=10, timeout=100)  # 100ms timeout
            
            for msg in messages:
                # Log all received messages for debugging
                hex_str = ' '.join(f'{b:02X}' for b in msg.data)
                self._log(f"Received CAN frame: {hex_str}")
                
                # Check if this is a BIT response start
                if len(received_data) == 0 and len(msg.data) >= 2:
                    if msg.data[0] == 0xF0 and msg.data[1] == 0x01:
                        received_data.extend(msg.data)
                        frame_count += 1
                        self._log(f"BIT response frame {frame_count} (start): {hex_str}")
                elif len(received_data) > 0 and len(received_data) < 20:
                    # Continue collecting multi-frame data
                    received_data.extend(msg.data)
                    frame_count += 1
                    self._log(f"BIT response frame {frame_count} (continuation): {hex_str}")
                    
                    # Check if we have complete 20-byte response
                    if len(received_data) >= 20:
                        self._log(f"Complete BIT response received: {len(received_data)} bytes")
                        # Parse BIT response
                        return self._parse_bit_response(received_data[:20])
            
            # Small delay between receive attempts
            if len(messages) == 0:
                time.sleep(0.02)  # 20ms delay if no messages received
        
        self._log(f"BIT query timeout - received {len(received_data)} bytes in {frame_count} frames")
        if len(received_data) > 0:
            hex_str = ' '.join(f'{b:02X}' for b in received_data)
            self._log(f"Partial data received: {hex_str}")
        return None
    
    def _parse_bit_response(self, data):
        """Parse BIT response data
        
        Args:
            data: Byte array with BIT response (at least 20 bytes)
            
        Returns:
            dict: Parsed BIT information
        """
        if len(data) < 20:
            self._log(f"Invalid BIT response length: {len(data)}")
            return None
        
        # Log received frame
        hex_str = ' '.join(f'{b:02X}' for b in data)
        self._log(f"RX Frame: {hex_str}")
        
        try:
            # Parse temperature (byte 2, signed)
            temperature = struct.unpack('b', bytes([data[2]]))[0]  # Signed byte
            
            # Parse voltage (bytes 3-4, unsigned, big-endian)
            voltage_raw = (data[3] << 8) | data[4]
            voltage = voltage_raw * 0.1  # Unit: 0.1V
            
            # Parse current (bytes 5-6, unsigned, big-endian)  
            current_raw = (data[5] << 8) | data[6]
            current = current_raw * 0.001  # Unit: 0.001A (mA)
            
            # Parse power (bytes 7-8, unsigned, big-endian)
            power_raw = (data[7] << 8) | data[8]
            power = power_raw * 0.01  # Unit: 0.01W (10mW)
            
            bit_info = {
                'temperature': temperature,
                'voltage': voltage,
                'current': current,
                'power': power
            }
            
            # Log parsed values
            self._log(f"BIT Info: Temp={temperature}°C, Voltage={voltage:.1f}V, "
                     f"Current={current:.3f}A, Power={power:.2f}W")
            
            # Also log to status if available
            if self.status_log_callback:
                status_msg = f"BIT信息:\n"
                status_msg += f"温度: {temperature}°C\n"
                status_msg += f"电压: {voltage:.1f}V\n"
                status_msg += f"电流: {current:.3f}A\n"
                status_msg += f"功率: {power:.2f}W"
                self.status_log_callback(status_msg)
            
            return bit_info
            
        except Exception as e:
            self._log(f"Error parsing BIT response: {e}")
            return None