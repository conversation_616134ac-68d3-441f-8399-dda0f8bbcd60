"""
Main Window for RF Module Test Application - V2.0
Simplified interface for 2-channel RF module control
"""

import sys
import time
from datetime import datetime
from ctypes import c_int
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTextEdit, QGroupBox, QGridLayout,
                             QLabel, QComboBox, QMessageBox, QApplication,
                             QSplitter, QTabWidget, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QTextCursor

from dual_channel_widget import DualChannelWidget
from bit_info_widget import BitInfoWidget
from usbcan2_interface import USBCAN2Interface
from rf_module_protocol import RFModuleProtocol


class CANReceiveThread(QThread):
    """Background thread for CAN message reception - optimized for speed"""
    
    message_received = pyqtSignal(list)
    
    def __init__(self, can_interface):
        super().__init__()
        self.can = can_interface
        self.running = False
        
    def run(self):
        """Thread main loop with batch receive optimization"""
        self.running = True
        while self.running:
            try:
                # Try batch receive first for better performance (like reference program)
                if hasattr(self.can, 'zcan') and hasattr(self.can, 'channel_handle') and self.can.channel_handle:
                    try:
                        # Receive up to 10 messages at once with short timeout
                        msgs, count = self.can.zcan.Receive(self.can.channel_handle, 10, c_int(50))
                        
                        if count > 0:
                            # Process all received messages
                            for i in range(count):
                                frame = msgs[i]
                                data = []
                                for j in range(frame.frame.can_dlc):
                                    data.append(frame.frame.data[j])
                                
                                # Emit each frame immediately
                                self.message_received.emit(data)
                        continue  # Skip fallback if batch receive worked
                    except:
                        pass  # Fall through to single receive
                
                # Fallback to single message receive
                success, data = self.can.receive_can_message(timeout=50)
                if success:
                    self.message_received.emit(data)
            except Exception as e:
                pass
                
    def stop(self):
        """Stop the thread"""
        self.running = False
        self.wait()


class MainWindow(QMainWindow):
    """Main application window for 2-channel RF control"""
    
    def __init__(self):
        super().__init__()
        self.can = USBCAN2Interface()
        self.protocol = RFModuleProtocol(self.can)
        self.protocol.set_log_callback(lambda msg: self.log(msg, prefix="PROTOCOL"))
        self.protocol.set_status_log_callback(self.log_to_status)
        self.receive_thread = None
        self.connected = False
        
        # Log limits
        self.max_status_lines = 500
        self.max_debug_lines = 1000
        
        self.init_ui()
        self.setWindowTitle("射频模块测试程序 V2.0 - 双通道")
        self.resize(1000, 700)
        
    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # Connection controls - compact single row
        conn_group = QGroupBox("CAN连接设置")
        conn_group.setMaximumHeight(60)  # Limit height for single row
        conn_layout = QHBoxLayout()
        conn_layout.setContentsMargins(10, 5, 10, 5)  # Reduce margins
        
        conn_layout.addWidget(QLabel("CAN通道:"))
        self.channel_combo = QComboBox()
        self.channel_combo.addItems(["通道 0", "通道 1"])
        self.channel_combo.setMaximumWidth(80)
        conn_layout.addWidget(self.channel_combo)
        
        self.btn_connect = QPushButton("连接")
        self.btn_connect.clicked.connect(self.toggle_connection)
        self.btn_connect.setMaximumHeight(25)
        self.btn_connect.setMaximumWidth(60)
        conn_layout.addWidget(self.btn_connect)
        
        conn_layout.addStretch()
        
        # Connection status label
        self.status_label = QLabel("未连接")
        self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        conn_layout.addWidget(self.status_label)
        
        conn_group.setLayout(conn_layout)
        main_layout.addWidget(conn_group)
        
        # Main control area with splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left side: Channel controls
        control_frame = QFrame()
        control_layout = QVBoxLayout(control_frame)
        
        # Dual channel control widget
        self.channel_widget = DualChannelWidget()
        self.channel_widget.send_requested.connect(self.send_channel_params)
        control_layout.addWidget(self.channel_widget)
        
        # BIT info widget
        self.bit_info_widget = BitInfoWidget()
        self.bit_info_widget.query_requested.connect(self.query_bit_info)
        control_layout.addWidget(self.bit_info_widget)
        
        control_layout.addStretch()
        splitter.addWidget(control_frame)
        
        # Right side: Log display
        log_frame = QFrame()
        log_layout = QVBoxLayout(log_frame)
        
        # Create tabbed log area
        self.log_tabs = QTabWidget()
        
        # Status log (human readable)
        self.status_log = QTextEdit()
        self.status_log.setReadOnly(True)
        self.status_log.setFont(QFont("Consolas", 9))
        self.log_tabs.addTab(self.status_log, "状态")
        
        # Debug log (technical details)
        self.debug_log = QTextEdit()
        self.debug_log.setReadOnly(True)
        self.debug_log.setFont(QFont("Consolas", 8))
        self.log_tabs.addTab(self.debug_log, "调试日志")
        
        # Clear logs button
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self.clear_logs)
        
        log_layout.addWidget(self.log_tabs)
        log_layout.addWidget(clear_btn)
        
        splitter.addWidget(log_frame)
        
        # Set splitter proportions
        splitter.setSizes([600, 400])
        main_layout.addWidget(splitter)
        
        # Initialize some default values
        self.log_to_status("程序启动完成\n请连接CAN设备开始测试")
        
    def toggle_connection(self):
        """Toggle CAN connection"""
        if not self.connected:
            self.connect_can()
        else:
            self.disconnect_can()
            
    def connect_can(self):
        """Connect to CAN device"""
        try:
            channel = self.channel_combo.currentIndex()
            
            self.log("正在连接CAN设备...", "CAN")
            self.log_to_status("正在连接CAN设备...")
            
            if self.can.connect(channel_index=channel):
                self.connected = True
                self.btn_connect.setText("断开")
                self.status_label.setText("已连接")
                self.status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
                self.channel_combo.setEnabled(False)
                
                # Start receive thread
                self.receive_thread = CANReceiveThread(self.can)
                self.receive_thread.message_received.connect(self.on_message_received)
                self.receive_thread.start()
                
                self.log("CAN连接成功", "CAN")
                self.log_to_status("CAN连接成功，可以开始测试")
                
            else:
                QMessageBox.warning(self, "连接失败", "无法连接到CAN设备，请检查设备连接")
                self.log("CAN连接失败", "CAN")
                
        except Exception as e:
            QMessageBox.critical(self, "连接错误", f"连接过程发生错误：{str(e)}")
            self.log(f"CAN连接异常: {str(e)}", "CAN")
            
    def disconnect_can(self):
        """Disconnect from CAN device"""
        try:
            # Stop receive thread
            if self.receive_thread and self.receive_thread.isRunning():
                self.receive_thread.stop()
                self.receive_thread = None
                
            # Disconnect CAN
            self.can.disconnect()
            
            self.connected = False
            self.btn_connect.setText("连接")
            self.status_label.setText("未连接")
            self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
            self.channel_combo.setEnabled(True)
            
            # Clear BIT info display
            self.bit_info_widget.clear_display()
            
            self.log("CAN连接已断开", "CAN")
            self.log_to_status("CAN连接已断开")
            
        except Exception as e:
            self.log(f"CAN断开异常: {str(e)}", "CAN")
            
    def send_channel_params(self, ch1_params, ch2_params):
        """Send parameters for both channels"""
        if not self.connected:
            QMessageBox.warning(self, "未连接", "请先连接CAN设备")
            return
            
        try:
            # Send parameters using protocol
            success = self.protocol.set_channel_params(ch1_params, ch2_params)
            
            if success:
                self.log("参数设置成功", "PROTOCOL")
                # Flash the button green briefly
                self.flash_success_feedback()
            else:
                self.log("参数设置失败", "PROTOCOL")
                QMessageBox.warning(self, "发送失败", "参数设置失败，请检查连接状态")
                
        except Exception as e:
            error_msg = f"参数设置异常: {str(e)}"
            self.log(error_msg, "PROTOCOL")
            QMessageBox.critical(self, "发送错误", error_msg)
            
    def flash_success_feedback(self):
        """Provide visual feedback for successful transmission"""
        # This could be enhanced with button color changes, etc.
        self.log_to_status("✓ 参数发送成功")
        
    def on_message_received(self, data):
        """Handle received CAN messages"""
        hex_str = ' '.join(f'{b:02X}' for b in data)
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        self.log(f"RX [{timestamp}]: {hex_str}", "CAN")
    
    def query_bit_info(self):
        """Query BIT information from the module"""
        if not self.connected:
            QMessageBox.warning(self, "未连接", "请先连接CAN设备")
            return
            
        try:
            self.log("查询BIT信息...", "BIT")
            
            # Temporarily pause the receive thread to avoid message conflicts
            if self.receive_thread and self.receive_thread.isRunning():
                self.receive_thread.running = False
                time.sleep(0.1)  # Give thread time to stop
            
            # Query BIT info through protocol
            bit_info = self.protocol.query_bit_info()
            
            # Restart receive thread
            if self.receive_thread:
                self.receive_thread.running = True
            
            if bit_info:
                # Update display
                self.log(f"BIT查询成功，准备更新显示: {bit_info}", "BIT")
                self.bit_info_widget.update_bit_info(bit_info)
                self.log("BIT信息查询成功", "BIT")
            else:
                self.log("BIT信息查询失败或超时", "BIT")
                self.bit_info_widget.update_bit_info(None)
                
        except Exception as e:
            self.log(f"BIT查询异常: {str(e)}", "BIT")
            self.bit_info_widget.update_bit_info(None)
            # Ensure receive thread is restarted
            if self.receive_thread:
                self.receive_thread.running = True
        
    def log(self, message, prefix=""):
        """Log message to debug log"""
        timestamp = time.strftime("%H:%M:%S")
        if prefix:
            log_message = f"[{timestamp}] [{prefix}] {message}"
        else:
            log_message = f"[{timestamp}] {message}"
            
        self.debug_log.append(log_message)
        
        # Limit number of lines
        self._limit_text_lines(self.debug_log, self.max_debug_lines)
        
        # Auto-scroll to bottom
        cursor = self.debug_log.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.debug_log.setTextCursor(cursor)
        
    def log_to_status(self, message):
        """Log message to status display"""
        timestamp = time.strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.status_log.append(log_message)
        
        # Limit number of lines
        self._limit_text_lines(self.status_log, self.max_status_lines)
        
        # Auto-scroll to bottom
        cursor = self.status_log.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.status_log.setTextCursor(cursor)
        
    def _limit_text_lines(self, text_edit, max_lines):
        """Limit the number of lines in a QTextEdit"""
        document = text_edit.document()
        if document.blockCount() > max_lines:
            # Remove excess lines from the beginning
            cursor = QTextCursor(document)
            cursor.movePosition(QTextCursor.Start)
            
            # Select and delete excess blocks
            excess_lines = document.blockCount() - max_lines
            for _ in range(excess_lines):
                cursor.select(QTextCursor.BlockUnderCursor)
                cursor.removeSelectedText()
                cursor.deleteChar()  # Remove the newline
        
    def clear_logs(self):
        """Clear all log displays"""
        self.debug_log.clear()
        self.status_log.clear()
        self.log_to_status("日志已清空")
        
    def closeEvent(self, event):
        """Handle window close event"""
        if self.connected:
            self.disconnect_can()
        event.accept()


def main():
    """Main entry point"""
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()