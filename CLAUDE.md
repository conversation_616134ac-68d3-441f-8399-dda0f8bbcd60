# CLAUDE.md - 上位机项目指导文档

本文件为Claude Code提供上位机项目的开发指导和历史记录。

## 项目概述

这是一个基于PyQt5的射频模块测试上位机程序，通过USBCAN-II设备与STM32F107下位机进行CAN通信。

### 技术栈
- **GUI框架**: PyQt5
- **CAN接口**: ZLG USBCAN-II (zlgcan库)
- **Python版本**: 3.x
- **通信协议**: 自定义CAN协议

## 项目架构

### 文件结构
```
上位机/V0.0/
├── main.py                    # 程序入口点
├── main_window_v2.py          # 主窗口界面 (V2.1包含BIT查询)
├── dual_channel_widget.py     # 双通道控制组件
├── bit_info_widget.py         # BIT信息显示组件 (V2.1新增)
├── rf_module_protocol.py      # CAN通信协议实现 (含BIT查询)
├── usbcan2_interface.py       # USBCAN-II硬件接口
├── zlgcan.py                  # ZLG CAN库Python包装
├── dll_loader.py              # DLL路径配置
├── doc/                       # 协议文档
│   └── CAN通信协议.md         # 最新通信协议规范
├── cankao/                    # 参考的原始项目文件
└── kerneldlls/                # ZLG CAN设备驱动库
```

### 关键组件

**1. 主窗口 (main_window_v2.py)**
- 简化的2通道控制界面
- CAN连接管理
- 日志显示系统
- 批量接收优化的CAN消息处理

**2. 双通道控件 (dual_channel_widget.py)**  
- 通道1: 微波频段 (2000-18000 MHz)
- 通道2: HF-UHF频段 (30-3000 MHz)
- 每通道独立的信号开关、工作模式、频率、衰减控制
- 自动发送防抖功能

**3. 协议处理 (rf_module_protocol.py)**
- 20字节单帧参数设置协议
- 多帧CAN传输 (自动分割为8+8+4字节)
- 参数验证和编码
- 性能优化的发送机制

**4. 硬件接口 (usbcan2_interface.py)**
- USBCAN-II设备连接和配置
- 500kbps CAN波特率
- 批量消息接收优化
- 多帧数据发送支持

## 协议规范

### CAN通信协议
基于`Doc/CAN通信协议.md`的简化2通道协议：

**帧格式**: 20字节单帧，通过多帧CAN传输
```
[0xF0][0x04][CH1_信号开关][CH1_工作模式][CH1_频率32位][CH1_衰减][CH1_校准]
[CH2_信号开关][CH2_工作模式][CH2_频率32位][CH2_衰减][CH2_校准][保留2字节]
```

**通道定义**:
- 通道1: 2000-18000 MHz (微波)
- 通道2: 30-3000 MHz (HF-UHF)
- 衰减范围: 0-50dB, 5dB步进
- 工作模式: 0=单点, 1=扫频
- 信号开关: 0=关闭, 1=开启

## 开发历史

### V2.0 重构 (2025-01-xx)

**重大变更**:
1. **简化架构**: 从复杂的10通道组系统简化为2独立通道
2. **协议更新**: 从多帧查询/设置改为单帧20字节设置
3. **UI重设计**: 垂直布局、单行控件组、直观操作

### V2.1 BIT查询功能 (2025-01-31)

**新增功能**:
1. **BIT信息查询**: 实现模块状态信息查询功能
   - 温度监控 (-55°C ~ 125°C)
   - 输入电压监控 (0.1V精度)
   - 电流监控 (1mA精度)
   - 功率监控 (10mW精度)
2. **自动刷新机制**: 支持定时自动查询BIT信息
3. **可视化显示**: 专用BIT信息显示组件，含状态指示

**技术实现**:

#### 协议层增强
- **新增查询命令**: CMD_QUERY_BIT = [0xF0, 0x01]
- **多帧接收处理**: 支持20字节响应分3帧接收
- **智能超时机制**: 2秒总超时，150ms初始延迟

#### 接收优化
- **线程冲突解决**: 查询时暂停后台接收线程
- **批量接收**: 单次可接收10条消息，100ms超时
- **调试增强**: 详细记录每帧接收情况

#### UI组件
- **BitInfoWidget**: 独立的BIT信息显示组件
- **实时状态显示**: 查询中/正常/失败三种状态
- **温度预警**: >60°C黄色预警，>80°C红色预警
- **自动刷新**: 1-60秒可调间隔

**具体修改**:

#### 协议层改进
- **移除查询功能**: 删除模块信息、BIT状态、通道参数查询
- **统一设置接口**: `set_channel_params(ch1_params, ch2_params)`
- **参数验证**: 各通道独立的频率范围和参数校验
- **编码优化**: 直接32位频率编码，无需BCD转换

#### 界面优化
- **布局改进**: 通道垂直排列，CAN连接区域紧凑化
- **控件重组**: 每行一个功能组(信号控制、工作模式、频率、衰减)
- **用户体验**: 自动发送防抖、实时参数显示、清晰的视觉分组

#### 性能优化  
- **批量接收**: 一次接收多达10个CAN消息，减少系统调用
- **去除等待**: 移除参数设置后的500ms确认等待
- **减少延时**: 帧间延时从10ms降至8ms，缓冲清理超时从10ms降至5ms
- **异常处理**: 批量接收失败时自动降级到单条接收

### 关键问题解决

**1. CAN发送失败 (AttributeError)**
- **问题**: USBCAN2Interface缺少`connect()`方法
- **解决**: 重命名`open_device()`为`connect()`，添加`disconnect()`方法

**2. 20字节帧发送失败**
- **问题**: CAN标准帧最大8字节，20字节单帧被拒绝
- **解决**: 使用`send_multi_frame()`自动分割为多个8字节帧

**3. QTextEdit属性错误**
- **问题**: PyQt5的QTextEdit没有`setMaximumBlockCount()`方法
- **解决**: 实现`_limit_text_lines()`手动控制日志行数

**4. 响应速度慢**
- **问题**: 单条接收、长时间等待确认、缓冲区清理慢
- **解决**: 批量接收、去除等待、优化延时参数

**5. BIT查询无响应**
- **问题**: 下位机需要100ms响应时间，接收线程抢占消息
- **解决**: 查询时暂停接收线程，增加初始延迟，延长接收超时

**6. BIT状态显示异常**
- **问题**: 查询成功后状态仍显示"查询中..."
- **解决**: 强制UI更新(QApplication.processEvents)，确保状态正确刷新

## 使用说明

### 环境要求
- Python 3.x
- PyQt5
- ZLG USBCAN-II设备及驱动
- Windows操作系统

### 运行步骤
1. 确保USBCAN-II设备已连接并安装驱动
2. 运行 `python main.py`
3. 选择CAN通道并点击"连接"
4. 在双通道界面设置参数
5. 参数会自动发送到下位机
6. 点击"查询BIT信息"获取模块状态
7. 可启用自动刷新定期获取状态

### 参数范围
- **通道1频率**: 2000-18000 MHz, 1MHz步进
- **通道2频率**: 30-3000 MHz, 1MHz步进  
- **衰减**: 0-50 dB, 5dB步进
- **工作模式**: 单点模式 / 扫频
- **信号开关**: 开启 / 关闭

### BIT信息说明
- **温度**: -55°C ~ 125°C (带预警显示)
- **输入电压**: 0.1V精度显示
- **电流**: 1mA精度显示 
- **功率**: 10mW精度显示
- **自动刷新**: 1-60秒可调间隔

### 故障排除
- **连接失败**: 检查USBCAN-II设备连接和驱动
- **发送失败**: 确认CAN总线连接和下位机状态  
- **界面无响应**: 检查Python环境和PyQt5安装
- **参数超范围**: 界面会显示红色提示，调整到有效范围
- **BIT查询无响应**: 检查下位机是否支持BIT查询命令(0xF0 0x01)
- **BIT数据异常**: 查看调试日志确认是否收到完整20字节响应

## 开发注意事项

### 代码规范
- 使用type hints提高代码可读性
- 异常处理要考虑硬件断开情况
- 界面操作使用信号槽机制避免阻塞
- CAN通信采用异步接收线程

### 性能考虑
- 避免在主线程中进行CAN通信
- 批量处理消息减少GUI更新频率
- 合理设置接收超时避免卡顿
- 缓冲区管理防止内存泄漏

### 扩展指导
- 新增通道: 修改协议帧格式和界面组件
- 协议变更: 重点关注编码/解码和验证逻辑
- 界面改进: 保持组件化设计，便于复用
- 硬件支持: 在usbcan2_interface.py中抽象硬件差异

## 测试验证

### 功能测试
- [x] CAN设备连接/断开
- [x] 双通道参数设置
- [x] 频率范围验证
- [x] 衰减步进验证
- [x] 界面响应性能
- [x] BIT信息查询功能
- [x] BIT自动刷新机制
- [ ] 长时间稳定性测试
- [ ] 异常恢复测试

### 性能指标
- CAN连接建立: <2秒
- 参数设置响应: <50ms
- 界面刷新频率: >30fps
- 内存使用: <100MB

## 版本历史

**V2.0 (当前)**
- 简化2通道架构
- 20字节单帧协议
- 性能优化界面
- 批量接收机制

**V1.x (参考)**
- 10通道组系统
- 多帧查询/设置协议
- 复杂消息队列机制
- 完整BIT状态监控

---
*此文档随项目开发持续更新*