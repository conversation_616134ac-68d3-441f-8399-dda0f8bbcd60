"""
RF Module CAN Protocol Implementation
Implements the CAN protocol for STM32F107-based RF modules
"""

import struct
import time
from datetime import datetime


class RFModuleProtocol:
    """RF Module protocol handler"""
    
    # Command IDs
    CMD_MODULE_INFO = [0xF0, 0x00]
    CMD_BIT_STATUS = [0xF0, 0x01]
    CMD_CHANNEL_PARAMS = [0xF0, 0x02]
    CMD_SET_PARAMS = [0xF0, 0x04]
    
    # Channel groups
    GROUP_ALL = 0x00
    GROUP_1_5 = 0x01
    GROUP_6_10 = 0x02
    
    # Bandwidth values
    BW_200KHZ = 0
    BW_5MHZ = 1
    BW_50MHZ = 2
    
    def __init__(self, can_interface):
        self.can = can_interface
        self.message_queue = None
        self.complete_message_queue = None
        self.log_callback = None
        self.status_log_callback = None
        
    def set_message_queue(self, queue):
        """Set the message queue for receiving messages"""
        self.message_queue = queue
        
    def set_complete_message_queue(self, queue):
        """Set the complete message queue for receiving assembled messages"""
        self.complete_message_queue = queue
        
    def set_log_callback(self, callback):
        """Set log callback for debug output"""
        self.log_callback = callback
        
    def set_status_log_callback(self, callback):
        """Set status log callback for human-readable status display"""
        self.status_log_callback = callback
        
    def _log(self, message):
        """Log message using callback or print"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(f"[Protocol] {message}")
        
    def query_module_info(self):
        """Query module product information
        
        Returns:
            dict: Module info or None if failed
        """
        # Record the query start time
        query_start = time.time()
        
        # Send query command
        if not self.can.send_can_message(self.CMD_MODULE_INFO):
            self._log("Failed to send module info query")
            return None
            
        # Wait for complete message
        if self.complete_message_queue:
            # Wait for module_info message
            start_time = time.time()
            timeout = 2.5  # Increased timeout to handle timing issues
            
            while (time.time() - start_time) < timeout:
                try:
                    msg = self.complete_message_queue.get(timeout=0.1)  # Shorter poll interval
                    
                    if msg:
                        msg_type, data = msg
                        if msg_type == 'module_info':
                            # Parse response
                            return self.parse_module_info(data)
                except Exception as e:
                    pass
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.01)
            
            return None
        else:
            # Fallback to direct receive if no queue
            time.sleep(0.1)
            success, data = self.can.receive_multi_frame(3, timeout=2000)
            if not success or len(data) < 21:
                self._log(f"Failed to receive module info response, got {len(data)} bytes")
                return None
                
            if data[0] != 0xF0 or data[1] != 0x00:
                self._log("Invalid module info response header")
                return None
                
            return self.parse_module_info(data)
        
    def parse_module_info(self, data):
        """Parse module info response data
        
        Args:
            data: 21 bytes of response data
            
        Returns:
            dict: Parsed module information
        """
        info = {}
        
        # Manufacturer code
        info['manufacturer'] = data[2]
        
        # Production date (BCD format: YYYY-MM-DD)
        year_bcd = (data[3] << 8) | data[4]
        month_bcd = data[5]
        day_bcd = data[6]
        info['production_date'] = self.decode_bcd_date(year_bcd, month_bcd, day_bcd)
        
        # Serial number (BCD format: YYYY-BB-NNN)
        serial_year_bcd = (data[7] << 8) | data[8]
        batch_bcd = data[9]
        serial_num_bcd = (data[10] << 8) | data[11]
        info['serial_number'] = self.decode_bcd_serial(serial_year_bcd, batch_bcd, serial_num_bcd)
        
        # MARK address
        info['mark_address'] = data[12]
        
        # Frequency range (big-endian, in kHz)
        freq_low = struct.unpack('>I', bytes(data[13:17]))[0]
        freq_high = struct.unpack('>I', bytes(data[17:21]))[0]
        info['freq_range'] = {
            'low': freq_low,
            'high': freq_high,
            'low_mhz': freq_low / 1000.0,
            'high_mhz': freq_high / 1000.0
        }
        
        return info
        
    def query_bit_status(self):
        """Query module BIT (Built-In Test) status
        
        Returns:
            dict: BIT status or None if failed
        """
        # Record the query start time
        query_start = time.time()
            
        # Send query command
        if not self.can.send_can_message(self.CMD_BIT_STATUS):
            self._log("Failed to send BIT status query")
            return None
            
        # Wait for complete message
        if self.complete_message_queue:
            # Wait for bit_status message
            start_time = time.time()
            timeout = 2.5  # Increased timeout to handle timing issues
            
            while (time.time() - start_time) < timeout:
                try:
                    msg = self.complete_message_queue.get(timeout=0.1)  # Shorter poll interval
                    if msg:
                        msg_type, data = msg
                        if msg_type == 'bit_status':
                            # Parse response
                            return self.parse_bit_status(data)
                except Exception as e:
                    self._log(f"DEBUG: Queue get error: {e}")
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.01)
            
            return None
        else:
            # Fallback to direct receive if no queue
            time.sleep(0.1)
            success, data = self.can.receive_multi_frame(2, timeout=2000)
            if not success or len(data) < 14:
                self._log(f"Failed to receive BIT status response, got {len(data)} bytes")
                return None
                
            if data[0] != 0xF0 or data[1] != 0x01:
                self._log("Invalid BIT status response header")
                return None
                
            return self.parse_bit_status(data)
        
    def parse_bit_status(self, data):
        """Parse BIT status response data
        
        Args:
            data: 14 bytes of response data
            
        Returns:
            dict: Parsed BIT status
        """
        status = {}
        
        # Temperature (signed byte, -55 to 125°C)
        temp = data[3]
        if temp > 127:
            temp = temp - 256  # Convert to signed
        status['temperature'] = temp
        
        # Channel voltages (0.1V precision, 0xFF = invalid)
        status['channel_voltages'] = []
        for i in range(10):
            voltage_raw = data[4 + i]
            if voltage_raw == 0xFF:
                voltage = None  # Invalid
            else:
                voltage = voltage_raw * 0.1
            status['channel_voltages'].append(voltage)
            
        return status
        
    def query_channel_params(self, channel_group=GROUP_ALL):
        """Query channel parameters
        
        Args:
            channel_group: GROUP_ALL, GROUP_1_5, or GROUP_6_10
            
        Returns:
            dict: Channel parameters or None if failed
        """
        # Record the query start time
        query_start = time.time()
            
        # Send query command
        cmd = self.CMD_CHANNEL_PARAMS + [channel_group]
        if not self.can.send_can_message(cmd):
            self._log("Failed to send channel params query")
            return None
            
        # Wait for complete message
        if self.complete_message_queue:
            # Wait for channel_params message
            start_time = time.time()
            timeout = 2.5  # Increased timeout to handle timing issues
            
            while (time.time() - start_time) < timeout:
                try:
                    msg = self.complete_message_queue.get(timeout=0.1)  # Shorter poll interval
                    if msg:
                        msg_type, data = msg
                        if msg_type == 'channel_params':
                            # Parse response
                            return self.parse_channel_params(data, channel_group)
                except Exception as e:
                    self._log(f"DEBUG: Queue get error: {e}")
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.01)
            
            return None
        else:
            # Fallback to direct receive if no queue
            time.sleep(0.1)
            success, data = self.can.receive_multi_frame(expected_frames, timeout=2000)
            if not success or len(data) < expected_bytes:
                self._log(f"Failed to receive channel params response, got {len(data)} bytes")
                return None
                
            if data[0] != 0xF0 or data[1] != 0x02:
                self._log("Invalid channel params response header")
                return None
                
            return self.parse_channel_params(data, channel_group)
        
    def parse_channel_params(self, data, channel_group):
        """Parse channel parameters response
        
        Args:
            data: Response data bytes
            channel_group: The requested channel group
            
        Returns:
            dict: Parsed parameters
        """
        params = {}
        
        if channel_group == self.GROUP_ALL:
            # Parse both groups
            params['group_1_5'] = self.parse_single_group(data[2:10])
            params['group_6_10'] = self.parse_single_group(data[9:17])
        else:
            # Parse single group
            group_name = 'group_1_5' if channel_group == self.GROUP_1_5 else 'group_6_10'
            params[group_name] = self.parse_single_group(data[2:10])
            
        return params
        
    def parse_single_group(self, data):
        """Parse single channel group parameters
        
        Args:
            data: 8 bytes of group data
            
        Returns:
            dict: Parsed group parameters
        """
        group = {}
        
        # RF attenuation (0-7 → 0-35 dB)
        group['rf_att_val'] = data[1]
        group['rf_att_db'] = data[1] * 5
        
        # IF attenuation (0-30 dB)
        group['if_att_db'] = data[2]
        
        # Frequency (kHz, big-endian)
        freq_khz = struct.unpack('>I', bytes(data[3:7]))[0]
        group['freq_khz'] = freq_khz
        group['freq_mhz'] = freq_khz / 1000.0
        
        # Control byte: bandwidth and work mode
        control = data[7]
        group['bandwidth'] = (control >> 1) & 0x03
        group['work_mode'] = control & 0x01
        group['power_on'] = (control & 0x01) == 0  # 0=ON, 1=OFF
        
        # Bandwidth string
        bw_map = {0: "200 kHz", 1: "5 MHz", 2: "50 MHz"}
        group['bandwidth_str'] = bw_map.get(group['bandwidth'], "Unknown")
        
        return group
        
    def set_channel_params(self, channel_group, freq_mhz, rf_att_db, 
                          if_att_db, bandwidth_idx, power_on):
        """Set channel parameters
        
        Args:
            channel_group: GROUP_ALL, GROUP_1_5, or GROUP_6_10
            freq_mhz: Frequency in MHz (30-3000)
            rf_att_db: RF attenuation in dB (0-35, 5dB steps)
            if_att_db: IF attenuation in dB (0-30)
            bandwidth_idx: Bandwidth index (0=200kHz, 1=5MHz, 2=50MHz)
            power_on: True for power ON, False for OFF
            
        Returns:
            bool: True if successful
        """
        # Log the human-readable parameters (for status display)
        group_names = {0x00: "所有通道", 0x01: "通道 1-5", 0x02: "通道 6-10"}
        bw_names = {0: "200 kHz", 1: "5 MHz", 2: "50 MHz"}
        
        if self.status_log_callback:
            status_msg = f"设置参数 - {group_names.get(channel_group, '未知')}\n"
            status_msg += f"  频率: {freq_mhz:.3f} MHz\n"
            status_msg += f"  射频衰减: {rf_att_db} dB\n"
            status_msg += f"  中频衰减: {if_att_db} dB\n"
            status_msg += f"  带宽: {bw_names.get(bandwidth_idx, '未知')}\n"
            status_msg += f"  电源: {'开启' if power_on else '关闭'}"
            self.status_log_callback(status_msg)
        
        # Validate parameters
        if not (30 <= freq_mhz <= 3000):
            self._log(f"Invalid frequency: {freq_mhz} MHz")
            return False
            
        if rf_att_db % 5 != 0 or not (0 <= rf_att_db <= 35):
            self._log(f"Invalid RF attenuation: {rf_att_db} dB")
            return False
            
        if not (0 <= if_att_db <= 30):
            self._log(f"Invalid IF attenuation: {if_att_db} dB")
            return False
            
        if bandwidth_idx not in [0, 1, 2]:
            self._log(f"Invalid bandwidth index: {bandwidth_idx}")
            return False
            
        # Convert values
        freq_khz = int(freq_mhz * 1000)
        rf_att_val = rf_att_db // 5
        work_mode = 0 if power_on else 1
        
        # Build control byte: (bw_valid << 4) | (bw_value << 1) | work_mode
        control_byte = (1 << 4) | (bandwidth_idx << 1) | work_mode
        
        # Frame 1: Command, channel group, control, and frequency (big-endian)
        frame1 = [
            0xF0, 0x04, channel_group, control_byte,
            (freq_khz >> 24) & 0xFF,
            (freq_khz >> 16) & 0xFF,
            (freq_khz >> 8) & 0xFF,
            freq_khz & 0xFF
        ]
        
        # Frame 2: Attenuation values
        frame2 = [rf_att_val, if_att_db]
        
        # Log the raw hex data (for log display)
        hex_str1 = ' '.join(f'{b:02X}' for b in frame1)
        hex_str2 = ' '.join(f'{b:02X}' for b in frame2)
        self._log(f"TX Frame 1: {hex_str1}")
        self._log(f"TX Frame 2: {hex_str2}")
        
        # Log detailed encoding information
        self._log(f"编码详情: freq_khz={freq_khz}, rf_att_val={rf_att_val}, "
                 f"work_mode={work_mode}, control_byte=0x{control_byte:02X}")
        
        # Clear receive buffer first
        self.can.clear_receive_buffer()
        
        # Send both frames
        if not self.can.send_can_message(frame1):
            self._log("Failed to send frame 1")
            return False
            
        # Small delay between frames
        time.sleep(0.008)  # 8ms delay
        
        if not self.can.send_can_message(frame2):
            self._log("Failed to send frame 2")
            return False
            
        return True
        
    def decode_bcd_date(self, year_bcd, month_bcd, day_bcd):
        """Decode BCD date format
        
        Args:
            year_bcd: Year in BCD (e.g., 0x2024)
            month_bcd: Month in BCD (e.g., 0x12)
            day_bcd: Day in BCD (e.g., 0x31)
            
        Returns:
            str: Formatted date string
        """
        year = ((year_bcd >> 12) & 0xF) * 1000 + \
               ((year_bcd >> 8) & 0xF) * 100 + \
               ((year_bcd >> 4) & 0xF) * 10 + \
               (year_bcd & 0xF)
               
        month = ((month_bcd >> 4) & 0xF) * 10 + (month_bcd & 0xF)
        day = ((day_bcd >> 4) & 0xF) * 10 + (day_bcd & 0xF)
        
        return f"{year:04d}-{month:02d}-{day:02d}"
        
    def decode_bcd_serial(self, year_bcd, batch_bcd, serial_bcd):
        """Decode BCD serial number format
        
        Args:
            year_bcd: Year in BCD
            batch_bcd: Batch number in BCD
            serial_bcd: Serial number in BCD
            
        Returns:
            str: Formatted serial number string
        """
        year = ((year_bcd >> 12) & 0xF) * 1000 + \
               ((year_bcd >> 8) & 0xF) * 100 + \
               ((year_bcd >> 4) & 0xF) * 10 + \
               (year_bcd & 0xF)
               
        batch = ((batch_bcd >> 4) & 0xF) * 10 + (batch_bcd & 0xF)
        
        serial = ((serial_bcd >> 12) & 0xF) * 1000 + \
                 ((serial_bcd >> 8) & 0xF) * 100 + \
                 ((serial_bcd >> 4) & 0xF) * 10 + \
                 (serial_bcd & 0xF)
        
        return f"{year:04d}-{batch:02d}-{serial:03d}"
    
    def set_product_info(self, manufacturer, prod_year_bcd, prod_month_bcd, 
                        prod_day_bcd, serial_year_bcd, serial_batch_bcd, 
                        serial_num_bcd):
        """Set product information (debug function)
        
        Args:
            manufacturer: Manufacturer code (0-255)
            prod_year_bcd: Production year in BCD
            prod_month_bcd: Production month in BCD
            prod_day_bcd: Production day in BCD
            serial_year_bcd: Serial year in BCD
            serial_batch_bcd: Serial batch in BCD
            serial_num_bcd: Serial number in BCD
            
        Returns:
            bool: True if successful
        """
        # Log the human-readable info
        if self.status_log_callback:
            prod_year = self.bcd_to_int(prod_year_bcd)
            prod_month = self.bcd_to_int(prod_month_bcd)
            prod_day = self.bcd_to_int(prod_day_bcd)
            serial_year = self.bcd_to_int(serial_year_bcd)
            serial_batch = self.bcd_to_int(serial_batch_bcd)
            serial_num = self.bcd_to_int(serial_num_bcd)
            
            # 厂家代号映射
            manufacturer_name = "十所" if manufacturer == 0 else str(manufacturer)
            
            status_msg = f"写入产品信息\n"
            status_msg += f"  厂家代号: {manufacturer_name} ({manufacturer})\n"
            status_msg += f"  出厂日期: {prod_year:04d}-{prod_month:02d}-{prod_day:02d}\n"
            status_msg += f"  序列号: {serial_year:04d}-{serial_batch:02d}-{serial_num:03d}"
            self.status_log_callback(status_msg)
        
        # Build command
        # Multi-frame message (12 bytes total)
        # Frame 1: Command + manufacturer + production date
        frame1 = [
            0xF0, 0x0E,  # Command
            manufacturer,
            (prod_year_bcd >> 8) & 0xFF,  # Year high byte
            prod_year_bcd & 0xFF,          # Year low byte
            prod_month_bcd,
            prod_day_bcd,
            (serial_year_bcd >> 8) & 0xFF  # Serial year high
        ]
        
        # Frame 2: Serial info
        frame2 = [
            serial_year_bcd & 0xFF,         # Serial year low
            serial_batch_bcd,
            (serial_num_bcd >> 8) & 0xFF,   # Serial num high
            serial_num_bcd & 0xFF           # Serial num low
        ]
        
        # Log the raw data
        hex_str1 = ' '.join(f'{b:02X}' for b in frame1)
        hex_str2 = ' '.join(f'{b:02X}' for b in frame2)
        self._log(f"TX Frame 1: {hex_str1}")
        self._log(f"TX Frame 2: {hex_str2}")
        
        # Clear receive buffer
        self.can.clear_receive_buffer()
        
        # Send frames
        if not self.can.send_can_message(frame1):
            self._log("Failed to send frame 1")
            return False
            
        time.sleep(0.008)  # 8ms delay
        
        if not self.can.send_can_message(frame2):
            self._log("Failed to send frame 2")
            return False
            
        # Wait for response
        time.sleep(0.1)
        success, data = self.can.receive_can_message(timeout=1000)
        
        if success and len(data) >= 3:
            if data[0] == 0xF0 and data[1] == 0x0E and data[2] == 0x00:
                self._log("Product info write confirmed")
                return True
            else:
                self._log(f"Unexpected response: {' '.join(f'{b:02X}' for b in data)}")
                return False
        else:
            self._log("No response received")
            return False
            
    def bcd_to_int(self, bcd_value):
        """Convert BCD to integer"""
        if bcd_value > 0xFF:
            # 4-digit BCD (year)
            return ((bcd_value >> 12) & 0xF) * 1000 + \
                   ((bcd_value >> 8) & 0xF) * 100 + \
                   ((bcd_value >> 4) & 0xF) * 10 + \
                   (bcd_value & 0xF)
        else:
            # 2-digit BCD
            return ((bcd_value >> 4) & 0xF) * 10 + (bcd_value & 0xF)