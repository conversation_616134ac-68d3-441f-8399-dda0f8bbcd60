('D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\RF_Module_Test_OneFile.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('main',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\main.py',
   'PYSOURCE'),
  ('kerneldlls\\CANDTU_NET.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANDTU_NET.dll',
   'BINARY'),
  ('kerneldlls\\CANDTU_x64.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANDTU_x64.dll',
   'BINARY'),
  ('kerneldlls\\CANDevCore.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANDevCore.dll',
   'BINARY'),
  ('kerneldlls\\CANDevice.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANDevice.dll',
   'BINARY'),
  ('kerneldlls\\CANETE.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANETE.dll',
   'BINARY'),
  ('kerneldlls\\CANET_TCP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANET_TCP.dll',
   'BINARY'),
  ('kerneldlls\\CANFDCOM.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANFDCOM.dll',
   'BINARY'),
  ('kerneldlls\\CANFDDTU.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANFDDTU.dll',
   'BINARY'),
  ('kerneldlls\\CANFDNET.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANFDNET.dll',
   'BINARY'),
  ('kerneldlls\\CANWIFI_TCP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANWIFI_TCP.dll',
   'BINARY'),
  ('kerneldlls\\CANWIFI_UDP.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\CANWIFI_UDP.dll',
   'BINARY'),
  ('kerneldlls\\USBCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\USBCAN.dll',
   'BINARY'),
  ('kerneldlls\\USBCANFD.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\USBCANFD.dll',
   'BINARY'),
  ('kerneldlls\\USBCANFD800U.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\USBCANFD800U.dll',
   'BINARY'),
  ('kerneldlls\\VirtualUSBCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\VirtualUSBCAN.dll',
   'BINARY'),
  ('kerneldlls\\ZPSCANFD.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPSCANFD.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\ZPSCANFD_IMPL.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\ZPSCANFD_IMPL.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\base.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\base.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\dataset.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\dataset.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\utils.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\utils.dll',
   'BINARY'),
  ('kerneldlls\\ZPS\\z_comm_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\z_comm_1.0.0.zdm',
   'BINARY'),
  ('kerneldlls\\ZPS\\zps_device_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\zps_device_1.0.0.zdm',
   'BINARY'),
  ('kerneldlls\\ZPS\\zps_function_1.0.0.zdm',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\zps_function_1.0.0.zdm',
   'BINARY'),
  ('kerneldlls\\ZlgCloud.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZlgCloud.dll',
   'BINARY'),
  ('ControlCAN.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\ControlCAN.dll',
   'BINARY'),
  ('zlgcan.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\zlgcan.dll',
   'BINARY'),
  ('python312.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python312.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('MSVCP120.dll', 'C:\\WINDOWS\\system32\\MSVCP120.dll', 'BINARY'),
  ('MSVCR120.dll', 'C:\\WINDOWS\\system32\\MSVCR120.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140.dll',
   'BINARY'),
  ('utils.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\utils.dll',
   'BINARY'),
  ('base.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\base.dll',
   'BINARY'),
  ('dataset.dll',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\dataset.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('kerneldlls\\USBCAN.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\USBCAN.xml',
   'DATA'),
  ('kerneldlls\\VCI_USBCAN2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\VCI_USBCAN2.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\aio_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\aio_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\async_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\async_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\can_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\can_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\devicevartype.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\devicevartype.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dio_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\dio_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dio_module.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\dio_module.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\dso_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\dso_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\import_initer.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\import_initer.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\imports.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\imports.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\receivedisturb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\receivedisturb.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\send_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\send_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dataset\\dataset_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\dataset\\dataset_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.ztdev',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\dev\\zps.ztdev',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\cpp_types.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\cpp_types.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\project_mgr.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\project_mgr.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\senddata_manager.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\senddata_manager.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\system.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\system.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\system_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\system_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\system\\zps_device_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\system\\zps_device_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\unit\\eye\\eye.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\unit\\eye\\eye.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\source\\zps\\zps_cpp_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\source\\zps\\zps_cpp_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\sys_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\sys_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\type_attrs.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\type_attrs.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\wave_cmd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\wave_cmd.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\zps_comm.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\zps_comm.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\device\\zps_type.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\device\\zps_type.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\function\\canfilter\\canfilter.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\function\\canfilter\\canfilter.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\resources\\function\\decode\\canwavedecode.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\resources\\function\\decode\\canwavedecode.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\fft_reference_wave_edit_dlg.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\fft_reference_wave_edit_dlg.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\func_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\func_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\math_func_mgr.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\math_func_mgr.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\tds_func_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\tds_func_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\test_fun_dialog.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\test_fun_dialog.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\upload_params_view.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\upload_params_view.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\user_input_config_view.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\user_input_config_view.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\user_input_param_dialog.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\user_input_param_dialog.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\base\\zcomm_config.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\base\\zcomm_config.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\ui\\default_ui_types.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\ui\\default_ui_types.xml',
   'DATA'),
  ('kerneldlls\\ZPS\\source\\zview\\ui\\default_widget_property.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\ZPS\\source\\zview\\ui\\default_widget_property.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\can.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\can.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-100ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\candtu-100ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-200ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\candtu-200ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-net-400.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\candtu-net-400.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu-net.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\candtu-net.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\candtu.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\candtu.ini',
   'DATA'),
  ('kerneldlls\\devices_property\\canet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdblue-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdblue-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdbridgeplus.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdbridgeplus.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdcom-100ie.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdcom-100ie.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfddtu-200.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfddtu-200.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfddtu-300.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfddtu-300.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet100-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet100-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet100-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet100-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet30cascade-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet30cascade-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet30cascade-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet30cascade-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet400u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet400u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet400u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet400u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet600u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet600u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet600u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet600u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet800u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet800u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdnet800u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdnet800u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdwifi-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdwifi-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canfdwifi-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canfdwifi-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\canscope.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\canscope.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\can.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\can.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-100ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\candtu-100ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-200ur.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\candtu-200ur.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-net-400.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\candtu-net-400.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu-net.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\candtu-net.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\candtu.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\candtu.ini',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdblue-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdblue-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdbridgeplus.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdbridgeplus.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdcom-100ie.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdcom-100ie.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfddtu-200.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfddtu-200.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfddtu-300.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfddtu-300.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet100-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet100-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet100-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet100-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet30cascade-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet30cascade-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet30cascade-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet30cascade-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet400u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet400u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet400u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet400u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet600u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet600u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet600u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet600u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet800u-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet800u-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdnet800u-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdnet800u-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdwifi-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdwifi-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canfdwifi-udp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canfdwifi-udp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\canscope.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\canscope.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\device_locale_strings.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\device_locale_strings.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pci-5010-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pci-5010-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pci-5020-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pci-5020-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-100u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pcie-canfd-100u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pcie-canfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-200u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pcie-canfd-200u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pcie-canfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-400u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pcie-canfd-400u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\pcie-canfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\pcie-canfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-2e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan-2e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-4e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan-4e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-8e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan-8e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan-e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan-e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan1.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan1.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan2.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcan4.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcan4.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcanfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcanfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcanfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-800h.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcanfd-800h.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\usbcanfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\usbcanfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\virtual.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\virtual.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\zpscanfd-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\zpscanfd-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\default\\zpscanfd-usb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\default\\zpscanfd-usb.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\device_locale_strings.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\device_locale_strings.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\mini-pcie-canfd.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\mini-pcie-canfd.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pci-5010-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pci-5010-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pci-5020-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pci-5020-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-100u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-100u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-1200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-1200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-200u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-200u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-400u-ex.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-400u-ex.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\pcie-canfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\pcie-canfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-2e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan-2e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-4e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan-4e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-8e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan-8e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan-e-u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan-e-u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan1.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan1.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan2.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan2.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcan4.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcan4.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-100u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcanfd-100u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-200u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcanfd-200u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-400u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcanfd-400u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-800h.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcanfd-800h.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\usbcanfd-800u.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\usbcanfd-800u.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\virtual.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\virtual.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\zpscanfd-tcp.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\zpscanfd-tcp.xml',
   'DATA'),
  ('kerneldlls\\devices_property\\zpscanfd-usb.xml',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\devices_property\\zpscanfd-usb.xml',
   'DATA'),
  ('kerneldlls\\dll_cfg.ini',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\kerneldlls\\dll_cfg.ini',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\MW_Work\\CAN\\cexiang\\STM32F107\\测试上位机\\Python\\V0.3\\build\\onefile_pack\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
