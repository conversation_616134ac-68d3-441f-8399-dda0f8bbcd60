# 版本 V0.3 更新说明

## 发布日期
2025-01-24

## 主要更新

### 频段快速切换功能
- **移除功能**: 删除了 ±1MHz 快速频率调节按钮
- **新增功能**: 在频率控制区域添加了频段切换按钮
  - "◀频段" 按钮：切换到上一频段（位于 -100 按钮左侧）
  - "频段▶" 按钮：切换到下一频段（位于 +100 按钮右侧）

### 频段划分
根据下位机的频段滤波器逻辑，划分为以下7个频段：
1. **频段1**: 30-200 MHz（中心频率：100 MHz）
2. **频段2**: 200-300 MHz（中心频率：250 MHz）
3. **频段3**: 300-450 MHz（中心频率：375 MHz）
4. **频段4**: 450-700 MHz（中心频率：575 MHz）
5. **频段5**: 700-1100 MHz（中心频率：900 MHz）
6. **频段6**: 1100-1800 MHz（中心频率：1450 MHz）
7. **频段7**: 1800-3000 MHz（中心频率：2400 MHz）

### 操作说明
- 点击"◀频段"按钮：切换到上一个频段的中心频率
- 点击"频段▶"按钮：切换到下一个频段的中心频率
- 在频段边界时会循环切换（如：频段1 → 频段7 → 频段1）
- 切换后自动发送新的频率参数（如启用了自动发送）

### 技术实现
- 在 `channel_control_widget.py` 中添加了 `get_current_band()`、`switch_to_prev_band()` 和 `switch_to_next_band()` 方法
- 频段判断基于当前频率值，自动识别所在频段
- 切换时直接设置到目标频段的中心频率

### 中频衰减控件改进
- **控件类型变更**: 将中频衰减从 SpinBox 输入框改为 ComboBox 下拉框
  - 支持 0-30 dB 范围，1dB 步进
  - 与射频衰减控件保持一致的交互方式
- **快速调节按钮**: 在下拉框两侧添加了 -1 和 +1 快速步进按钮
  - 方便快速微调中频衰减值
  - 点击后立即发送参数

## 文件变更
- 修改：`channel_control_widget.py`
  - 删除了 ±1MHz 按钮相关代码
  - 添加了频段切换按钮和相关方法
  - 将中频衰减 EnhancedSpinBox 替换为 QComboBox
  - 添加了 `adjust_if_att()` 方法处理中频衰减步进
  - 更新了 `setEnabled`、`get_parameters` 和 `set_parameters` 方法
  - 移除了不再使用的 `EnhancedSpinBox` 类

## 兼容性
- 与 V0.2 版本完全兼容
- 不影响其他功能的正常使用