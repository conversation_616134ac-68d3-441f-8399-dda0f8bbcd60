# 产品信息设置功能说明

## 功能概述

V0.1版本新增了产品信息设置功能，这是一个**调试功能**，允许修改设备的出厂信息。

⚠️ **警告**：此功能会永久修改设备的产品信息，请谨慎使用！

## 界面位置

产品信息设置区域位于界面右侧下方，与日志/状态显示区域垂直分隔。界面紧凑，高度限制在150像素内。

## 功能组件

### 输入控件（紧凑布局）
第一行：
- **厂家代号**: 0-255的数字
- **出厂日期**: 日期选择器，支持日历弹出

第二行：
- **序列号**: 年份(2000-2099) - 批次(1-99) - 序号(1-999)

### 操作按钮（右侧）
- **读取**: 查询设备当前的产品信息（蓝色）
- **写入**: 将输入的信息写入设备（红色警示）

### 显示区域（底部）
- 灰色小字体显示当前设备的产品信息

## 自动功能

- **连接后自动读取**: 设备连接成功500ms后，自动读取产品信息并填充到编辑框
- **写入后自动验证**: 写入成功100ms后，自动读取验证

## CAN协议

### 写入命令 (0xF0 0x0E)

**数据格式**（共12字节，分2帧发送）：

帧1（8字节）：
```
BYTE0-1: 0xF0 0x0E (命令)
BYTE2:   厂家代号
BYTE3-4: 出厂年份 (BCD, 大端)
BYTE5:   出厂月份 (BCD)
BYTE6:   出厂日期 (BCD)
BYTE7:   序列号年份高字节 (BCD)
```

帧2（4字节）：
```
BYTE0:   序列号年份低字节 (BCD)
BYTE1:   序列号批次 (BCD)
BYTE2-3: 序列号序号 (BCD, 大端)
```

**响应格式**：
```
BYTE0-1: 0xF0 0x0E
BYTE2:   0x00 (成功) / 其他 (失败)
```

## BCD编码示例

- 年份 2024 → 0x2024
- 月份 12 → 0x12
- 日期 31 → 0x31
- 批次 99 → 0x99
- 序号 123 → 0x0123

## 操作流程

1. **读取当前值**
   - 点击"读取当前值"按钮
   - 系统自动查询模块信息
   - 在显示区域显示当前值

2. **修改产品信息**
   - 在输入控件中设置新值
   - 点击"写入新值"按钮
   - 系统弹出确认对话框
   - 确认后发送写入命令
   - 自动读取验证是否写入成功

## 日志记录

### 状态标签页显示
```
[14:30:25] 写入产品信息
  厂家代号: 1
  出厂日期: 2024-12-31
  序列号: 2024-01-001
```

### 日志标签页显示
```
[PROTOCOL] TX Frame 1: F0 0E 01 20 24 12 31 20
[PROTOCOL] TX Frame 2: 24 01 00 01
[CAN] RX: F0 0E 00
[PROTOCOL] Product info write confirmed
```

## 安全措施

1. **确认对话框**: 写入前必须确认
2. **警示颜色**: 写入按钮使用红色警示
3. **自动验证**: 写入后自动读取验证
4. **日志记录**: 完整记录操作过程

## 注意事项

- 此功能仅用于生产调试
- 修改后的信息会永久保存在设备Flash中
- 建议在修改前先读取并记录原始信息
- 序列号应保持唯一性