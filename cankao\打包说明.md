# RF Module Test V0.2 打包说明

## 概述
本文档说明如何将RF Module Test V0.2打包成Windows可执行文件(exe)。

## 打包方案对比

### 1. 文件夹版本（推荐）
- **优点**：
  - 启动速度快
  - DLL加载更可靠
  - 便于调试和更新
  - 文件结构清晰
- **缺点**：
  - 需要整个文件夹才能运行
  - 分发时需要打包整个目录

### 2. 单文件版本
- **优点**：
  - 只有一个exe文件，便于分发
  - 用户使用简单
- **缺点**：
  - 启动较慢（需要解压临时文件）
  - 文件较大（约100MB+）
  - 某些杀毒软件可能误报

## 快速开始

### 方法一：使用批处理脚本（最简单）

1. **打包为文件夹版本**：
   ```batch
   双击运行 build_folder.bat
   ```

2. **打包为单文件版本**：
   ```batch
   双击运行 build_onefile.bat
   ```

### 方法二：使用Python脚本

1. **安装依赖**：
   ```bash
   pip install pyinstaller
   ```

2. **运行打包脚本**：
   ```bash
   # 文件夹版本
   python build_exe.py folder
   
   # 单文件版本
   python build_exe.py onefile
   ```

## 打包脚本功能

`build_exe.py`脚本提供了以下功能：

1. **自动安装PyInstaller**：如果未安装会自动安装
2. **生成spec配置文件**：自动配置所有依赖
3. **处理DLL依赖**：正确打包所有CAN库文件
4. **保持目录结构**：kerneldlls目录结构完整保留
5. **清理旧文件**：自动清理之前的构建文件

## 特殊处理

### 1. DLL文件处理
脚本会自动包含以下DLL：
- `zlgcan.dll` - ZLG CAN主库
- `ControlCAN.dll` - 控制CAN库
- `kerneldlls/` - 所有设备驱动DLL
- `kerneldlls/devices_property/` - XML配置文件

### 2. PyQt5依赖
- 自动包含PyQt5所需的所有模块
- 隐藏控制台窗口（GUI程序）

### 3. 路径处理
- 单文件版本使用运行时钩子设置DLL搜索路径
- 确保程序能找到所有依赖的DLL文件

## 输出文件位置

### 文件夹版本
```
dist/
└── RF_Module_Test_V0.2/
    ├── RF_Module_Test_V0.2.exe    # 主程序
    ├── zlgcan.dll                  # CAN库
    ├── ControlCAN.dll              # 控制库
    ├── kerneldlls/                 # 驱动目录
    └── ...                         # 其他依赖
```

### 单文件版本
```
dist/
└── RF_Module_Test_V0.2.exe        # 独立可执行文件
```

## 常见问题

### 1. 打包失败：找不到模块
**解决方案**：确保在V0.2目录下运行打包脚本

### 2. 运行时找不到DLL
**解决方案**：
- 使用文件夹版本
- 检查kerneldlls目录是否完整
- 确保没有遗漏任何DLL文件

### 3. 杀毒软件误报
**解决方案**：
- 添加到杀毒软件白名单
- 使用文件夹版本（误报率较低）
- 考虑代码签名（需要证书）

### 4. 程序启动慢
**解决方案**：
- 使用文件夹版本而非单文件版本
- 首次启动会较慢，后续会快一些

## 发布建议

### 1. 测试
- 在没有Python环境的电脑上测试
- 测试所有功能是否正常
- 验证CAN通信功能

### 2. 打包发布
```
文件夹版本：
1. 将整个 RF_Module_Test_V0.2 文件夹压缩为zip
2. 用户解压后直接运行exe即可

单文件版本：
1. 直接分发 RF_Module_Test_V0.2.exe
2. 用户双击即可运行
```

### 3. 版本信息
考虑添加版本信息文件，在打包时包含：
- 版本号：0.2.0
- 公司名称：您的公司
- 产品名称：RF Module Test
- 版权信息：Copyright © 2025

## 高级选项

### 1. 添加图标
1. 准备一个.ico文件
2. 修改spec文件中的`icon=None`为`icon='your_icon.ico'`

### 2. 优化文件大小
在spec文件中添加排除项：
```python
excludes=['matplotlib', 'numpy', 'pandas', 'scipy', 'PIL', 'tkinter']
```

### 3. 调试版本
如需调试信息，修改spec文件：
- `debug=True` - 启用调试模式
- `console=True` - 显示控制台窗口

## 维护建议

1. **保留spec文件**：便于后续更新时使用
2. **版本控制**：将spec文件加入版本控制
3. **自动化构建**：可以集成到CI/CD流程
4. **定期测试**：每次更新后都要测试打包版本