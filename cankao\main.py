"""
RF Module Test Application
Main entry point for the application
"""

# 首先设置DLL路径
from dll_loader import setup_dll_paths
setup_dll_paths()

import sys
from PyQt5.QtWidgets import QApplication
from main_window import MainWindow


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("射频模块测试")
    app.setOrganizationName("射频测试")
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Run application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()