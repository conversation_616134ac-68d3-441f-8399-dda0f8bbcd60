# V0.1 版本特性

## 发布日期
2025-01-03

## 主要特性

### 1. 增强型频率控制
- **快捷步进按钮**: -100/-10/-1/+1/+10/+100 MHz快速调节
- **鼠标滚轮支持**: 
  - 普通滚轮: 1 MHz步进
  - Ctrl+滚轮: 10 MHz步进 
  - Shift+滚轮: 0.1 MHz步进
- **频率范围**: 30-3000 MHz，精度到0.001 MHz

### 2. 改进的衰减控制
- **射频衰减下拉框**: 0-35 dB (5dB步进)，配备±5快捷按钮
- **中频衰减数字框**: 0-30 dB (1dB步进)，支持滚轮控制
- **视觉反馈**: 参数发送时按钮颜色变化(绿→蓝→绿)

### 3. 自动发送功能
- **默认启用**: 参数改变后自动发送
- **智能防抖**: 10ms延迟，避免频繁发送
- **帧间延迟**: 8ms，确保可靠传输
- **可选开关**: 支持切换到手动发送模式

### 4. 双重日志系统
- **日志标签页**: 显示原始CAN帧数据(十六进制)
- **状态标签页**: 显示人类可读的解析数据
- **自动切换**: 查询成功后自动切换到状态标签页

### 5. 产品信息设置
- **调试功能**: 支持修改设备出厂信息
- **BCD编码**: 日期和序列号使用BCD格式
- **安全确认**: 写入前弹出警告对话框
- **自动读取**: 连接后500ms自动读取产品信息

### 6. 优化的界面布局
- **紧凑设计**: 
  - 频率独占一行，带快捷按钮
  - 衰减独占一行，射频/中频分开
  - 带宽和电源在第三行
  - 发送控制在第四行
- **重新排序**:
  - 通道控制在上方(常用功能)
  - 查询操作在下方
  - 产品信息在日志上方
- **间距优化**: 列间距8px，行间距适中

### 7. 性能优化
- **查询响应**: 从2.5秒减少到30ms
- **多线程架构**: GUI保持响应，后台处理协议
- **消息队列**: 可靠的多帧消息组装
- **实时更新**: 参数改变立即发送(10ms延迟)

## 技术改进

### 后端优化
- 修复了Qt信号延迟问题(使用QueryWorker线程)
- 修复了CAN接收超时问题(正确的c_int包装)
- 修复了消息队列竞态条件
- 修复了频率数据解析(大端字节序)
- 改进了线程安全性

### 前端增强
- 自定义EnhancedDoubleSpinBox/EnhancedSpinBox控件
- QTimer防抖机制
- 视觉反馈动画
- 工具提示说明

## 已知限制
- 需要Windows系统(使用Windows DLL)
- 需要USBCAN-II硬件
- 单设备支持

## 依赖项
- PyQt5 >= 5.15.0
- Python 3.6+
- ZLG USBCAN-II驱动

## 文件结构
```
V0.1/
├── main.py                      # 应用入口
├── main_window.py               # 主窗口(含产品信息设置)
├── channel_control_widget.py    # 增强型通道控制组件
├── rf_module_protocol.py        # 协议实现(含双重日志)
├── usbcan2_interface.py        # CAN接口封装
├── can_message_queue.py        # 消息队列
├── can_frame_assembler.py      # 帧组装器
├── zlgcan.py                   # ZLG官方Python封装
├── requirements.txt            # Python依赖
├── run.bat                     # Windows启动脚本
├── 界面布局说明.md             # UI布局文档
├── 产品信息设置说明.md         # 产品信息功能文档
└── 版本特性.md                 # 本文档
```

## 升级指南

从V0.0升级到V0.1:
1. 备份V0.0配置(如有)
2. 安装V0.1到新目录
3. 运行`pip install -r requirements.txt`
4. 使用`run.bat`启动程序

## 下一版本计划
- 配置文件支持(保存/加载预设)
- 数据导出功能
- 批量通道设置
- 跨平台支持(Linux/macOS)