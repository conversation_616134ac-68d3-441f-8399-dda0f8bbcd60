# V0.1 版本说明

## 版本信息
- 版本号：V0.1
- 发布日期：2025-01-03
- 基于版本：V0.0

## 新增功能

### 1. 界面布局优化
- **分行显示**：
  - 第一行：射频频率控制（独占一行）
  - 第二行：射频衰减和中频衰减（独占一行）
  - 第三行：带宽选择和电源控制
  - 第四行：自动发送和手动发送控制
- **边框分组**：每个功能区域使用边框分隔，层次更清晰
- **统一标签宽度**：所有标签固定宽度，界面更整齐

### 2. 快捷步进控制
- **频率控制**：新增 `-100`、`-10`、`-1`、`+1`、`+10`、`+100` MHz 快捷按钮
- **射频衰减**：新增 `-5`、`+5` dB 快捷按钮
- 方便快速调节参数，无需手动输入

### 3. 鼠标滚轮支持
- **频率调节**：
  - 普通滚轮：1 MHz 步进
  - Ctrl + 滚轮：10 MHz 步进
  - Shift + 滚轮：0.1 MHz 步进
- **中频衰减调节**：
  - 普通滚轮：1 dB 步进
  - Ctrl + 滚轮：10 dB 步进
- 需要先点击控件获取焦点后才能使用滚轮

### 4. 自动发送功能（默认启用）
- 新增"自动发送"复选框，**默认勾选**
- 启用后，参数改变时自动发送（延迟500ms）
- 避免频繁发送，提高操作效率
- 手动发送按钮仍然可用

### 5. 界面美化
- **绿色发送按钮**：醒目的绿色按钮，鼠标悬停和点击有不同效果
- **发送反馈**：发送时按钮短暂变蓝，提供视觉反馈
- **工具提示**：添加滚轮操作和自动发送的说明提示
- **控件间距**：优化布局间距，视觉更舒适

## 使用说明

### 快捷操作
1. **快速调频**：使用 -100/-10/-1/+1/+10/+100 按钮快速调节
2. **滚轮调节**：点击数值框后使用鼠标滚轮
3. **自动应用**：默认开启自动发送，参数改变即生效

### 键盘快捷键
- `Ctrl + 滚轮`：大步进调节（10倍）
- `Shift + 滚轮`：小步进调节（0.1倍）

### 界面布局示意
```
┌─────────────────────────────────────────┐
│ 射频频率 (MHz): [-100][-10][-1][100.000][+1][+10][+100] │
├─────────────────────────────────────────┤
│ 射频衰减 (dB): [-5][0▼][+5]  中频衰减 (dB): [0]        │
├─────────────────────────────────────────┤
│ 带宽: [5 MHz▼]              ☑ 电源开启                  │
├─────────────────────────────────────────┤
│ ☑ 自动发送                          [手动发送]          │
└─────────────────────────────────────────┘
```

## 技术改进
- 实现了自定义的 `EnhancedDoubleSpinBox` 和 `EnhancedSpinBox` 类
- 添加了防抖动定时器，避免频繁发送
- 参数更新时临时禁用自动发送，防止意外触发
- 使用 QFrame 实现功能区域分组

## 兼容性
- 完全兼容 V0.0 的所有功能
- 保持原有 API 接口不变
- 可无缝替换旧版本