# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[
        ('zlgcan.dll', '.'),
        ('ControlCAN.dll', '.'),
    ],
    datas=[
        ('kerneldlls', 'kerneldlls'),
    ],
    hiddenimports=['PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,      # 包含所有二进制文件
    a.zipfiles,      # 包含所有zip文件
    a.datas,         # 包含所有数据文件
    [],
    name='RF_Module_Test_OneFile',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,        # 可选：使用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
# 注意：单文件版本没有COLLECT部分