"""
Modern UI Styles for RF Module Test Application
Provides consistent styling across all components
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import <PERSON>F<PERSON>, <PERSON><PERSON><PERSON><PERSON>, QColor


class ModernStyles:
    """Modern UI styling constants and methods"""
    
    # Color Palette
    PRIMARY_COLOR = "#2196F3"      # Blue
    PRIMARY_DARK = "#1976D2"       # Dark Blue
    PRIMARY_LIGHT = "#BBDEFB"      # Light Blue
    
    SECONDARY_COLOR = "#4CAF50"    # Green
    SECONDARY_DARK = "#388E3C"     # Dark Green
    SECONDARY_LIGHT = "#C8E6C9"    # Light Green
    
    ACCENT_COLOR = "#FF9800"       # Orange
    ACCENT_DARK = "#F57C00"        # Dark Orange
    ACCENT_LIGHT = "#FFE0B2"       # Light Orange
    
    ERROR_COLOR = "#F44336"        # Red
    WARNING_COLOR = "#FF9800"      # Orange
    SUCCESS_COLOR = "#4CAF50"      # Green
    INFO_COLOR = "#2196F3"         # Blue
    
    # Background Colors
    BACKGROUND_PRIMARY = "#FAFAFA"    # Very Light Gray
    BACKGROUND_SECONDARY = "#FFFFFF"  # White
    BACKGROUND_DARK = "#37474F"       # Dark Gray
    
    # Text Colors
    TEXT_PRIMARY = "#212121"       # Dark Gray
    TEXT_SECONDARY = "#757575"     # Medium Gray
    TEXT_HINT = "#BDBDBD"         # Light Gray
    TEXT_WHITE = "#FFFFFF"        # White
    
    # Border and Shadow
    BORDER_COLOR = "#E0E0E0"       # Light Gray
    SHADOW_COLOR = "rgba(0, 0, 0, 0.1)"
    
    @staticmethod
    def get_main_window_style():
        """Get main window stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            color: {ModernStyles.TEXT_PRIMARY};
        }}
        
        QWidget {{
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
            font-size: 9pt;
        }}
        """
    
    @staticmethod
    def get_group_box_style():
        """Get group box stylesheet"""
        return f"""
        QGroupBox {{
            font-weight: bold;
            font-size: 10pt;
            color: {ModernStyles.TEXT_PRIMARY};
            border: 2px solid {ModernStyles.BORDER_COLOR};
            border-radius: 8px;
            margin-top: 12px;
            padding-top: 8px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            color: {ModernStyles.PRIMARY_COLOR};
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}
        """
    
    @staticmethod
    def get_button_style(button_type="primary"):
        """Get button stylesheet based on type"""
        if button_type == "primary":
            bg_color = ModernStyles.PRIMARY_COLOR
            bg_hover = ModernStyles.PRIMARY_DARK
            text_color = ModernStyles.TEXT_WHITE
        elif button_type == "success":
            bg_color = ModernStyles.SECONDARY_COLOR
            bg_hover = ModernStyles.SECONDARY_DARK
            text_color = ModernStyles.TEXT_WHITE
        elif button_type == "warning":
            bg_color = ModernStyles.WARNING_COLOR
            bg_hover = ModernStyles.ACCENT_DARK
            text_color = ModernStyles.TEXT_WHITE
        else:  # secondary
            bg_color = ModernStyles.BACKGROUND_SECONDARY
            bg_hover = ModernStyles.BORDER_COLOR
            text_color = ModernStyles.TEXT_PRIMARY
            
        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: bold;
            font-size: 9pt;
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {bg_hover};
        }}
        
        QPushButton:pressed {{
            background-color: {bg_hover};
            transform: translateY(1px);
        }}
        
        QPushButton:disabled {{
            background-color: {ModernStyles.BORDER_COLOR};
            color: {ModernStyles.TEXT_HINT};
        }}
        """
    
    @staticmethod
    def get_input_style():
        """Get input controls stylesheet"""
        return f"""
        QSpinBox, QDoubleSpinBox, QComboBox {{
            border: 2px solid {ModernStyles.BORDER_COLOR};
            border-radius: 4px;
            padding: 4px 8px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            color: {ModernStyles.TEXT_PRIMARY};
            font-size: 9pt;
        }}
        
        QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border-color: {ModernStyles.PRIMARY_COLOR};
        }}
        
        QSpinBox:hover, QDoubleSpinBox:hover, QComboBox:hover {{
            border-color: {ModernStyles.PRIMARY_LIGHT};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {ModernStyles.TEXT_SECONDARY};
            margin-right: 4px;
        }}
        """
    
    @staticmethod
    def get_checkbox_style():
        """Get checkbox stylesheet"""
        return f"""
        QCheckBox {{
            color: {ModernStyles.TEXT_PRIMARY};
            font-size: 9pt;
            spacing: 8px;
        }}
        
        QCheckBox::indicator {{
            width: 16px;
            height: 16px;
            border: 2px solid {ModernStyles.BORDER_COLOR};
            border-radius: 3px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}
        
        QCheckBox::indicator:hover {{
            border-color: {ModernStyles.PRIMARY_COLOR};
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {ModernStyles.PRIMARY_COLOR};
            border-color: {ModernStyles.PRIMARY_COLOR};
            image: none;
        }}
        
        QCheckBox::indicator:checked:hover {{
            background-color: {ModernStyles.PRIMARY_DARK};
        }}
        """
    
    @staticmethod
    def get_text_edit_style():
        """Get text edit stylesheet"""
        return f"""
        QTextEdit {{
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 4px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            color: {ModernStyles.TEXT_PRIMARY};
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 8pt;
            padding: 8px;
        }}
        
        QTextEdit:focus {{
            border-color: {ModernStyles.PRIMARY_COLOR};
        }}
        """
    
    @staticmethod
    def get_tab_widget_style():
        """Get tab widget stylesheet"""
        return f"""
        QTabWidget::pane {{
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 4px;
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
        }}
        
        QTabBar::tab {{
            background-color: {ModernStyles.BACKGROUND_PRIMARY};
            color: {ModernStyles.TEXT_SECONDARY};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }}
        
        QTabBar::tab:selected {{
            background-color: {ModernStyles.PRIMARY_COLOR};
            color: {ModernStyles.TEXT_WHITE};
        }}
        
        QTabBar::tab:hover:!selected {{
            background-color: {ModernStyles.PRIMARY_LIGHT};
            color: {ModernStyles.TEXT_PRIMARY};
        }}
        """
    
    @staticmethod
    def get_status_label_style(status_type="normal"):
        """Get status label stylesheet"""
        if status_type == "connected":
            color = ModernStyles.SUCCESS_COLOR
        elif status_type == "error":
            color = ModernStyles.ERROR_COLOR
        elif status_type == "warning":
            color = ModernStyles.WARNING_COLOR
        else:
            color = ModernStyles.TEXT_SECONDARY
            
        return f"""
        QLabel {{
            color: {color};
            font-weight: bold;
            font-size: 9pt;
            padding: 4px 8px;
            border-radius: 4px;
            background-color: rgba({color[1:3]}, {color[3:5]}, {color[5:7]}, 0.1);
        }}
        """
    
    @staticmethod
    def get_tool_button_style():
        """Get tool button stylesheet"""
        return f"""
        QToolButton {{
            background-color: {ModernStyles.BACKGROUND_SECONDARY};
            border: 1px solid {ModernStyles.BORDER_COLOR};
            border-radius: 4px;
            color: {ModernStyles.TEXT_PRIMARY};
            font-weight: bold;
            padding: 4px 8px;
        }}
        
        QToolButton:hover {{
            background-color: {ModernStyles.PRIMARY_LIGHT};
            border-color: {ModernStyles.PRIMARY_COLOR};
        }}
        
        QToolButton:pressed {{
            background-color: {ModernStyles.PRIMARY_COLOR};
            color: {ModernStyles.TEXT_WHITE};
        }}
        """
