"""
Channel Control Widget for RF Module Test
Provides UI controls for setting RF channel parameters with enhanced features:
- Quick step buttons for frequency control
- Mouse wheel support for frequency and attenuation
- Dropdown for attenuation selection
- Auto-send on value change option (enabled by default)
"""

from PyQt5.QtWidgets import (QGroupBox, QGridLayout, QLabel, QDoubleSpinBox,
                             QComboBox, QSpinBox, QCheckBox, QPushButton,
                             QHBoxLayout, QVBoxLayout, QToolButton, QFrame)
from PyQt5.QtCore import pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont


class EnhancedDoubleSpinBox(QDoubleSpinBox):
    """Enhanced QDoubleSpinBox with mouse wheel support"""
    
    # Custom signal for wheel events
    wheelChanged = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFocusPolicy(Qt.StrongFocus)
        
    def wheelEvent(self, event):
        """Handle mouse wheel events for fine control"""
        if self.hasFocus():
            # Get wheel delta
            delta = event.angleDelta().y()
            
            # Determine step size based on modifiers
            if event.modifiers() & Qt.ControlModifier:
                # Ctrl + wheel: 10x step
                step = self.singleStep() * 10
            elif event.modifiers() & Qt.ShiftModifier:
                # Shift + wheel: 0.1x step  
                step = self.singleStep() * 0.1
            else:
                # Normal wheel: 1x step
                step = self.singleStep()
                
            # Apply the change
            if delta > 0:
                self.setValue(self.value() + step)
            else:
                self.setValue(self.value() - step)
                
            # Emit custom signal for wheel changes
            self.wheelChanged.emit()
                
            event.accept()
        else:
            event.ignore()
            
    def keyPressEvent(self, event):
        """Handle key press events"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.editingFinished.emit()
            event.accept()
        else:
            super().keyPressEvent(event)




class ChannelControlWidget(QGroupBox):
    """Widget for controlling a channel group with enhanced features"""
    
    # Signal emitted when Send button is clicked or auto-send triggers
    send_clicked = pyqtSignal(dict)
    
    def __init__(self, title, channel_group, parent=None):
        super().__init__(title, parent)
        self.channel_group = channel_group
        self.auto_send_enabled = True  # 默认启用自动发送
        self.auto_send_timer = QTimer()
        self.auto_send_timer.timeout.connect(self.auto_send)
        self.auto_send_timer.setSingleShot(True)
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI components"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(8)  # 适中的行间距
        
        # Row 1: RF Frequency (独占一行)
        freq_frame = QFrame()
        freq_frame.setFrameStyle(QFrame.NoFrame)  # 无边框
        freq_layout = QHBoxLayout(freq_frame)
        freq_layout.setContentsMargins(5, 5, 5, 5)
        
        freq_label = QLabel("射频频率 (MHz):")
        freq_label.setMinimumWidth(100)
        freq_layout.addWidget(freq_label)
        
        # Quick step buttons for frequency
        self.freq_prev_band = QToolButton()
        self.freq_prev_band.setText("◀频段")
        self.freq_prev_band.clicked.connect(self.switch_to_prev_band)
        self.freq_prev_band.setToolTip("切换到上一频段")
        freq_layout.addWidget(self.freq_prev_band)
        
        self.freq_minus_100 = QToolButton()
        self.freq_minus_100.setText("-100")
        self.freq_minus_100.clicked.connect(lambda: self.adjust_frequency(-100))
        freq_layout.addWidget(self.freq_minus_100)
        
        self.freq_minus_10 = QToolButton()
        self.freq_minus_10.setText("-10")
        self.freq_minus_10.clicked.connect(lambda: self.adjust_frequency(-10))
        freq_layout.addWidget(self.freq_minus_10)
        
        self.rf_freq = EnhancedDoubleSpinBox()
        self.rf_freq.setRange(30.0, 3000.0)
        self.rf_freq.setDecimals(3)
        self.rf_freq.setSingleStep(1.0)
        self.rf_freq.setValue(100.0)
        self.rf_freq.setMinimumWidth(120)
        self.rf_freq.setToolTip("使用鼠标滚轮调节频率\nCtrl+滚轮: 10倍步进\nShift+滚轮: 0.1倍步进\n按回车键发送")
        self.rf_freq.editingFinished.connect(self.on_value_changed)
        self.rf_freq.wheelChanged.connect(self.on_value_changed)
        freq_layout.addWidget(self.rf_freq)
        
        self.freq_plus_10 = QToolButton()
        self.freq_plus_10.setText("+10")
        self.freq_plus_10.clicked.connect(lambda: self.adjust_frequency(10))
        freq_layout.addWidget(self.freq_plus_10)
        
        self.freq_plus_100 = QToolButton()
        self.freq_plus_100.setText("+100")
        self.freq_plus_100.clicked.connect(lambda: self.adjust_frequency(100))
        freq_layout.addWidget(self.freq_plus_100)
        
        self.freq_next_band = QToolButton()
        self.freq_next_band.setText("频段▶")
        self.freq_next_band.clicked.connect(self.switch_to_next_band)
        self.freq_next_band.setToolTip("切换到下一频段")
        freq_layout.addWidget(self.freq_next_band)
        
        freq_layout.addStretch()
        main_layout.addWidget(freq_frame)
        
        # 添加水平分隔线
        line1 = QFrame()
        line1.setFrameShape(QFrame.HLine)
        line1.setFrameShadow(QFrame.Sunken)
        line1.setStyleSheet("QFrame { color: #e0e0e0; }")
        main_layout.addWidget(line1)
        
        # Row 2: Attenuation (衰减独占一行)
        att_frame = QFrame()
        att_frame.setFrameStyle(QFrame.NoFrame)  # 无边框
        att_layout = QHBoxLayout(att_frame)
        att_layout.setContentsMargins(5, 5, 5, 5)
        
        # RF Attenuation
        rf_att_label = QLabel("射频衰减 (dB):")
        rf_att_label.setMinimumWidth(100)
        att_layout.addWidget(rf_att_label)
        
        self.rf_att_minus = QToolButton()
        self.rf_att_minus.setText("-5")
        self.rf_att_minus.clicked.connect(lambda: self.adjust_rf_att(-5))
        att_layout.addWidget(self.rf_att_minus)
        
        self.rf_att = QComboBox()
        self.rf_att.setMinimumWidth(80)
        # RF attenuation in 5dB steps
        for i in range(0, 40, 5):
            self.rf_att.addItem(f"{i}")
        self.rf_att.setCurrentIndex(0)  # Default 0dB
        self.rf_att.currentIndexChanged.connect(self.on_value_changed)
        att_layout.addWidget(self.rf_att)
        
        self.rf_att_plus = QToolButton()
        self.rf_att_plus.setText("+5")
        self.rf_att_plus.clicked.connect(lambda: self.adjust_rf_att(5))
        att_layout.addWidget(self.rf_att_plus)
        
        att_layout.addSpacing(15)
        
        # IF Attenuation
        if_att_label = QLabel("中频衰减 (dB):")
        att_layout.addWidget(if_att_label)
        
        self.if_att_minus = QToolButton()
        self.if_att_minus.setText("-1")
        self.if_att_minus.clicked.connect(lambda: self.adjust_if_att(-1))
        att_layout.addWidget(self.if_att_minus)
        
        self.if_att = QComboBox()
        self.if_att.setMinimumWidth(80)
        # IF attenuation in 1dB steps from 0 to 30
        for i in range(31):
            self.if_att.addItem(f"{i}")
        self.if_att.setCurrentIndex(0)  # Default 0dB
        self.if_att.currentIndexChanged.connect(self.on_value_changed)
        att_layout.addWidget(self.if_att)
        
        self.if_att_plus = QToolButton()
        self.if_att_plus.setText("+1")
        self.if_att_plus.clicked.connect(lambda: self.adjust_if_att(1))
        att_layout.addWidget(self.if_att_plus)
        
        att_layout.addStretch()
        main_layout.addWidget(att_frame)
        
        # 添加水平分隔线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setFrameShadow(QFrame.Sunken)
        line2.setStyleSheet("QFrame { color: #e0e0e0; }")
        main_layout.addWidget(line2)
        
        # Row 3: Bandwidth and Power (带宽和电源控制)
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.NoFrame)  # 无边框
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(5, 5, 5, 5)
        
        bw_label = QLabel("带宽:")
        bw_label.setMinimumWidth(100)
        control_layout.addWidget(bw_label)
        
        self.rf_bw = QComboBox()
        self.rf_bw.setMinimumWidth(100)
        self.rf_bw.addItems(["200 kHz", "5 MHz", "50 MHz"])
        self.rf_bw.setCurrentIndex(1)  # Default 5MHz
        self.rf_bw.currentIndexChanged.connect(self.on_value_changed)
        control_layout.addWidget(self.rf_bw)
        
        control_layout.addSpacing(30)
        
        self.power_ctrl = QCheckBox("电源开启")
        self.power_ctrl.setChecked(True)
        self.power_ctrl.stateChanged.connect(self.on_value_changed)
        control_layout.addWidget(self.power_ctrl)
        
        control_layout.addStretch()
        main_layout.addWidget(control_frame)
        
        # 添加水平分隔线
        line3 = QFrame()
        line3.setFrameShape(QFrame.HLine)
        line3.setFrameShadow(QFrame.Sunken)
        line3.setStyleSheet("QFrame { color: #e0e0e0; }")
        main_layout.addWidget(line3)
        
        # Row 4: Send controls (发送控制)
        send_frame = QFrame()
        send_layout = QHBoxLayout(send_frame)
        send_layout.setContentsMargins(5, 5, 5, 5)
        
        self.auto_send_checkbox = QCheckBox("自动发送")
        self.auto_send_checkbox.setChecked(True)  # 默认勾选
        self.auto_send_checkbox.setToolTip("数据改变时自动发送（延迟10ms）")
        self.auto_send_checkbox.stateChanged.connect(self.on_auto_send_changed)
        send_layout.addWidget(self.auto_send_checkbox)
        
        send_layout.addStretch()
        
        self.btn_send = QPushButton("手动发送")
        self.btn_send.setMinimumWidth(100)
        self.btn_send.clicked.connect(self.on_send_clicked)
        # Make send button more prominent
        font = self.btn_send.font()
        font.setBold(True)
        self.btn_send.setFont(font)
        self.btn_send.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        send_layout.addWidget(self.btn_send)
        
        main_layout.addWidget(send_frame)
        
        self.setLayout(main_layout)
        
    def adjust_frequency(self, delta):
        """Adjust frequency by delta MHz"""
        new_value = self.rf_freq.value() + delta
        self.rf_freq.setValue(new_value)
        # 快速步进按钮点击后立即发送
        self.on_value_changed()
        
    def adjust_rf_att(self, delta):
        """Adjust RF attenuation by delta dB"""
        current_index = self.rf_att.currentIndex()
        if delta > 0 and current_index < self.rf_att.count() - 1:
            self.rf_att.setCurrentIndex(current_index + 1)
            # 快速步进按钮点击后立即发送
            self.on_value_changed()
        elif delta < 0 and current_index > 0:
            self.rf_att.setCurrentIndex(current_index - 1)
            # 快速步进按钮点击后立即发送
            self.on_value_changed()
            
    def get_current_band(self):
        """Get current frequency band based on frequency value"""
        freq_mhz = self.rf_freq.value()
        
        # Define band center frequencies (MHz)
        bands = [
            (100.0, 30, 200),      # Band 1: 30-200 MHz (center: 100 MHz)
            (250.0, 200, 300),     # Band 2: 200-300 MHz (center: 250 MHz)
            (375.0, 300, 450),     # Band 3: 300-450 MHz (center: 375 MHz)
            (575.0, 450, 700),     # Band 4: 450-700 MHz (center: 575 MHz)
            (900.0, 700, 1100),    # Band 5: 700-1100 MHz (center: 900 MHz)
            (1450.0, 1100, 1800),  # Band 6: 1100-1800 MHz (center: 1450 MHz)
            (2400.0, 1800, 3000)   # Band 7: 1800-3000 MHz (center: 2400 MHz)
        ]
        
        # Find current band
        for i, (center, min_freq, max_freq) in enumerate(bands):
            if min_freq <= freq_mhz < max_freq:
                return i, bands
                
        # Default to band based on proximity
        if freq_mhz < 200:
            return 0, bands
        else:
            return 6, bands
            
    def switch_to_prev_band(self):
        """Switch to previous frequency band"""
        current_band, bands = self.get_current_band()
        
        # Calculate previous band (with wrapping)
        prev_band = (current_band - 1) % len(bands)
        
        # Set frequency to center of previous band
        center_freq = bands[prev_band][0]
        self.rf_freq.setValue(center_freq)
        
        # Trigger immediate send
        self.on_value_changed()
        
    def switch_to_next_band(self):
        """Switch to next frequency band"""
        current_band, bands = self.get_current_band()
        
        # Calculate next band (with wrapping)
        next_band = (current_band + 1) % len(bands)
        
        # Set frequency to center of next band
        center_freq = bands[next_band][0]
        self.rf_freq.setValue(center_freq)
        
        # Trigger immediate send
        self.on_value_changed()
        
    def adjust_if_att(self, delta):
        """Adjust IF attenuation by delta dB"""
        current_index = self.if_att.currentIndex()
        if delta > 0 and current_index < self.if_att.count() - 1:
            self.if_att.setCurrentIndex(current_index + 1)
            # 快速步进按钮点击后立即发送
            self.on_value_changed()
        elif delta < 0 and current_index > 0:
            self.if_att.setCurrentIndex(current_index - 1)
            # 快速步进按钮点击后立即发送
            self.on_value_changed()
            
    def on_value_changed(self):
        """Handle value change events"""
        if self.auto_send_enabled:
            # Reset timer to avoid sending too frequently
            self.auto_send_timer.stop()
            self.auto_send_timer.start(10)  # 10ms delay
            
    def on_auto_send_changed(self, state):
        """Handle auto-send checkbox state change"""
        self.auto_send_enabled = state == Qt.Checked
        if not self.auto_send_enabled:
            self.auto_send_timer.stop()
            
    def auto_send(self):
        """Automatically send parameters after delay"""
        self.on_send_clicked()
        
    def on_send_clicked(self):
        """Handle Send button click"""
        # Collect all parameters
        params = {
            'channel_group': self.channel_group,
            'freq_mhz': self.rf_freq.value(),
            'rf_att_db': int(self.rf_att.currentText()),
            'if_att_db': int(self.if_att.currentText()),
            'bandwidth_idx': self.rf_bw.currentIndex(),
            'power_on': self.power_ctrl.isChecked()
        }
        
        # Emit signal with parameters
        self.send_clicked.emit(params)
        
        # Visual feedback - brief color change
        original_style = self.btn_send.styleSheet()
        self.btn_send.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 5px;
                border-radius: 3px;
            }
        """)
        QTimer.singleShot(200, lambda: self.btn_send.setStyleSheet(original_style))
        
    def set_parameters(self, params):
        """Set widget parameters from dict
        
        Args:
            params: Dictionary with channel parameters
        """
        # Temporarily disable auto-send during parameter update
        was_auto_send = self.auto_send_enabled
        self.auto_send_enabled = False
        
        if 'freq_mhz' in params:
            self.rf_freq.setValue(params['freq_mhz'])
            
        if 'rf_att_db' in params:
            # Find the index for this attenuation value
            att_text = str(params['rf_att_db'])
            index = self.rf_att.findText(att_text)
            if index >= 0:
                self.rf_att.setCurrentIndex(index)
                
        if 'if_att_db' in params:
            # Find the index for this attenuation value
            att_text = str(params['if_att_db'])
            index = self.if_att.findText(att_text)
            if index >= 0:
                self.if_att.setCurrentIndex(index)
            
        if 'bandwidth' in params:
            self.rf_bw.setCurrentIndex(params['bandwidth'])
            
        if 'power_on' in params:
            self.power_ctrl.setChecked(params['power_on'])
            
        # Restore auto-send state
        self.auto_send_enabled = was_auto_send
            
    def get_parameters(self):
        """Get current widget parameters
        
        Returns:
            dict: Current channel parameters
        """
        return {
            'channel_group': self.channel_group,
            'freq_mhz': self.rf_freq.value(),
            'rf_att_db': int(self.rf_att.currentText()),
            'if_att_db': int(self.if_att.currentText()),
            'bandwidth_idx': self.rf_bw.currentIndex(),
            'power_on': self.power_ctrl.isChecked()
        }
        
    def set_enabled(self, enabled):
        """Enable or disable all controls
        
        Args:
            enabled: True to enable, False to disable
        """
        self.rf_freq.setEnabled(enabled)
        self.rf_bw.setEnabled(enabled)
        self.rf_att.setEnabled(enabled)
        self.if_att.setEnabled(enabled)
        self.power_ctrl.setEnabled(enabled)
        self.btn_send.setEnabled(enabled)
        self.freq_prev_band.setEnabled(enabled)
        self.freq_minus_100.setEnabled(enabled)
        self.freq_minus_10.setEnabled(enabled)
        self.freq_plus_10.setEnabled(enabled)
        self.freq_plus_100.setEnabled(enabled)
        self.freq_next_band.setEnabled(enabled)
        self.rf_att_minus.setEnabled(enabled)
        self.rf_att_plus.setEnabled(enabled)
        self.if_att_minus.setEnabled(enabled)
        self.if_att_plus.setEnabled(enabled)
        self.auto_send_checkbox.setEnabled(enabled)